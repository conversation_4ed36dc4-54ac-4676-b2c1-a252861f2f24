"""
Multi-step trajectory generation with risk-based selection.
Generates multiple complete trajectories and selects the one with minimum risk.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass

from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel


@dataclass
class TrajectoryCandidate:
    """A complete trajectory candidate with associated risk."""
    states: torch.Tensor          # [horizon, state_dim] - predicted states
    actions: torch.Tensor         # [horizon, 2] - actions taken
    total_risk: float             # Total risk for this trajectory
    step_risks: torch.Tensor      # [horizon] - risk at each step
    probabilities: torch.Tensor   # [horizon] - probability of each action


class MultiStepTrajectoryGenerator(nn.Module):
    """
    Generates multiple complete trajectories and selects based on minimum risk.
    """
    
    def __init__(self, 
                 trajectory_predictor: LSTMTrajectoryPredictor,
                 risk_model: EnhancedRiskModel,
                 action_sampler: ActionSampler,
                 horizon: int = 10,
                 num_trajectories: int = 5,
                 sampling_strategy: str = 'top_k'):
        """
        Initialize multi-step trajectory generator.
        
        Args:
            trajectory_predictor: LSTM trajectory predictor
            risk_model: Risk model for scoring
            action_sampler: Action sampler
            horizon: Number of steps to predict
            num_trajectories: Number of trajectory candidates to generate
            sampling_strategy: 'top_k', 'random', or 'beam_search'
        """
        super().__init__()
        self.trajectory_predictor = trajectory_predictor
        self.risk_model = risk_model
        self.action_sampler = action_sampler
        self.horizon = horizon
        self.num_trajectories = num_trajectories
        self.sampling_strategy = sampling_strategy
        
        # Create choice model for action selection
        self.choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
    
    def generate_trajectory_candidates(self, 
                                     state_history: torch.Tensor,
                                     current_state: torch.Tensor,
                                     intended_path: Optional[torch.Tensor] = None) -> List[TrajectoryCandidate]:
        """
        Generate multiple trajectory candidates.
        
        Args:
            state_history: [B, T, state_dim] - historical states
            current_state: [B, state_dim] - current state
            intended_path: Optional intended path
            
        Returns:
            List of trajectory candidates
        """
        batch_size = current_state.shape[0]
        device = current_state.device
        candidate_actions = self.action_sampler.get_candidates(device)
        
        trajectories = []
        
        for traj_idx in range(self.num_trajectories):
            # Generate one complete trajectory
            trajectory_states = []
            trajectory_actions = []
            trajectory_risks = []
            trajectory_probs = []
            
            # Start from current state
            current_step_state = current_state.clone()
            step_history = state_history.clone()
            
            for step in range(self.horizon):
                # Get action probabilities for current step
                probabilities, scores = self.choice_model(
                    step_history, current_step_state, candidate_actions, intended_path
                )
                
                # Sample action based on strategy
                if self.sampling_strategy == 'top_k':
                    # Sample from top-k actions
                    k = min(5, candidate_actions.shape[0])
                    top_k_probs, top_k_indices = torch.topk(probabilities, k, dim=0)
                    # Renormalize top-k probabilities
                    top_k_probs = top_k_probs / top_k_probs.sum(dim=0, keepdim=True)
                    # Sample from top-k
                    sampled_idx = torch.multinomial(top_k_probs.T, 1).squeeze()  # [B]
                    action_indices = top_k_indices[sampled_idx, torch.arange(batch_size)]
                    
                elif self.sampling_strategy == 'random':
                    # Sample randomly from all actions
                    action_indices = torch.multinomial(probabilities.T, 1).squeeze()  # [B]
                    
                else:  # 'greedy'
                    # Always select best action
                    action_indices = torch.argmax(probabilities, dim=0)  # [B]
                
                # Get selected actions
                selected_actions = candidate_actions[action_indices]  # [B, 2]
                selected_probs = probabilities[action_indices, torch.arange(batch_size)]  # [B]
                
                # Predict next state
                next_state = current_step_state.clone()
                next_state[:, :2] += selected_actions  # Update position
                
                # Compute step risk
                step_risk = self.risk_model.compute_collision_risk(
                    next_state.unsqueeze(0), selected_actions.unsqueeze(0), step_history
                ).squeeze(0)  # [B]
                
                # Store step information
                trajectory_states.append(next_state)
                trajectory_actions.append(selected_actions)
                trajectory_risks.append(step_risk)
                trajectory_probs.append(selected_probs)
                
                # Update for next step
                current_step_state = next_state
                # Update history by appending new state
                step_history = torch.cat([step_history[:, 1:], next_state.unsqueeze(1)], dim=1)
            
            # Create trajectory candidate for each batch item
            for b in range(batch_size):
                states = torch.stack([s[b] for s in trajectory_states])  # [horizon, state_dim]
                actions = torch.stack([a[b] for a in trajectory_actions])  # [horizon, 2]
                risks = torch.stack([r[b] for r in trajectory_risks])  # [horizon]
                probs = torch.stack([p[b] for p in trajectory_probs])  # [horizon]
                
                total_risk = risks.sum().item()
                
                trajectory = TrajectoryCandidate(
                    states=states,
                    actions=actions,
                    total_risk=total_risk,
                    step_risks=risks,
                    probabilities=probs
                )
                trajectories.append(trajectory)
        
        return trajectories
    
    def select_best_trajectory(self, 
                             trajectories: List[TrajectoryCandidate],
                             selection_criteria: str = 'min_risk') -> TrajectoryCandidate:
        """
        Select the best trajectory based on criteria.
        
        Args:
            trajectories: List of trajectory candidates
            selection_criteria: 'min_risk', 'max_prob', or 'balanced'
            
        Returns:
            Best trajectory candidate
        """
        if selection_criteria == 'min_risk':
            # Select trajectory with minimum total risk
            best_idx = min(range(len(trajectories)), key=lambda i: trajectories[i].total_risk)
            
        elif selection_criteria == 'max_prob':
            # Select trajectory with maximum probability product
            best_idx = max(range(len(trajectories)), 
                          key=lambda i: trajectories[i].probabilities.prod().item())
            
        elif selection_criteria == 'balanced':
            # Balance between low risk and high probability
            scores = []
            for traj in trajectories:
                risk_score = -traj.total_risk  # Lower risk is better
                prob_score = traj.probabilities.prod().log().item()  # Log probability
                balanced_score = risk_score + prob_score
                scores.append(balanced_score)
            best_idx = max(range(len(scores)), key=lambda i: scores[i])
            
        else:
            raise ValueError(f"Unknown selection criteria: {selection_criteria}")
        
        return trajectories[best_idx]
    
    def forward(self, 
                state_history: torch.Tensor,
                current_state: torch.Tensor,
                intended_path: Optional[torch.Tensor] = None,
                selection_criteria: str = 'min_risk') -> Tuple[TrajectoryCandidate, List[TrajectoryCandidate]]:
        """
        Generate multiple trajectories and select the best one.
        
        Args:
            state_history: [B, T, state_dim] - historical states
            current_state: [B, state_dim] - current state
            intended_path: Optional intended path
            selection_criteria: Criteria for selecting best trajectory
            
        Returns:
            Tuple of (best_trajectory, all_trajectories)
        """
        # Generate multiple trajectory candidates
        all_trajectories = self.generate_trajectory_candidates(
            state_history, current_state, intended_path
        )
        
        # Group trajectories by batch item
        batch_size = current_state.shape[0]
        batch_trajectories = [[] for _ in range(batch_size)]
        
        for i, traj in enumerate(all_trajectories):
            batch_idx = i % batch_size
            batch_trajectories[batch_idx].append(traj)
        
        # Select best trajectory for each batch item
        best_trajectories = []
        for batch_trajs in batch_trajectories:
            best_traj = self.select_best_trajectory(batch_trajs, selection_criteria)
            best_trajectories.append(best_traj)
        
        return best_trajectories, all_trajectories
    
    def predict_pedestrian_trajectory(self,
                                    state_history: torch.Tensor,
                                    current_state: torch.Tensor,
                                    num_samples: int = 10) -> Dict:
        """
        Predict pedestrian trajectory with risk analysis.
        
        Args:
            state_history: Historical states
            current_state: Current state
            num_samples: Number of trajectory samples to generate
            
        Returns:
            Dictionary with trajectory predictions and risk analysis
        """
        # Temporarily set number of trajectories
        original_num_traj = self.num_trajectories
        self.num_trajectories = num_samples
        
        # Generate trajectories
        best_trajectories, all_trajectories = self.forward(
            state_history, current_state, selection_criteria='min_risk'
        )
        
        # Restore original setting
        self.num_trajectories = original_num_traj
        
        # Analyze results
        batch_size = len(best_trajectories)
        results = []
        
        for b in range(batch_size):
            best_traj = best_trajectories[b]
            batch_trajs = [traj for i, traj in enumerate(all_trajectories) if i % batch_size == b]
            
            # Extract pedestrian positions (first 2 dimensions)
            ped_positions = best_traj.states[:, :2].cpu().numpy()  # [horizon, 2]
            
            # Risk analysis
            risks = [traj.total_risk for traj in batch_trajs]
            
            result = {
                'best_trajectory': {
                    'positions': ped_positions,
                    'states': best_traj.states.cpu().numpy(),
                    'actions': best_traj.actions.cpu().numpy(),
                    'total_risk': best_traj.total_risk,
                    'step_risks': best_traj.step_risks.cpu().numpy()
                },
                'risk_analysis': {
                    'min_risk': min(risks),
                    'max_risk': max(risks),
                    'mean_risk': np.mean(risks),
                    'std_risk': np.std(risks),
                    'all_risks': risks
                },
                'trajectory_diversity': {
                    'num_candidates': len(batch_trajs),
                    'risk_range': max(risks) - min(risks)
                }
            }
            results.append(result)
        
        return results if batch_size > 1 else results[0]
