# # train_all.py

# import os
# import math
# import argparse
# import torch
# import torch.nn as nn
# import torch.optim as optim
# import numpy as np
# from torch.utils.data import DataLoader
# from sklearn.metrics import mean_squared_error

# # import your modules
# from vr_dataset import VRPedestrianDataset
# from pred_lstm    import LSTMGaussianPredictor, gaussian_nll
# from risk_model   import RiskModel
# from sampler      import sample_candidates_gaussian

# # Softmax‐NLL over K candidates
# def softmax_nll(R, idx_obs):
#     """
#     R: Tensor (K, B) = risk+cost scores
#     idx_obs: LongTensor (B,) = index of observed candidate
#     returns: scalar loss
#     """
#     # we want P_obs = exp(-R_obs) / sum_j exp(-R_j)
#     # NLL = -log P_obs = R_obs + logsumexp(R)
#     R_obs = R[idx_obs, torch.arange(R.shape[1])]   # (B,)
#     logZ  = torch.logsumexp(R, dim=0)              # (B,)
#     return torch.mean(R_obs + logZ)

# # ADE/FDE computation
# def compute_ade_fde(gt, pred):
#     """
#     gt, pred: np.array (B, T_pred, 2)
#     returns: ADE, FDE
#     """
#     diffs = gt - pred
#     dists = np.linalg.norm(diffs, axis=2)          # (B, T_pred)
#     ADE = dists.mean()
#     FDE = dists[:, -1].mean()
#     return ADE, FDE

# def train_all(args):
#     device = 'cuda' if torch.cuda.is_available() else 'cpu'

#     # 1) Dataset
#     dataset = VRPedestrianDataset(
#         data_path=args.data_dir,
#         obs_len=args.obs_len,
#         pred_len=args.pred_len
#     )
#     print("Dataloaded")
#     # train/val split
#     n = int(len(dataset)*args.train_frac)
#     train_ds, val_ds = torch.utils.data.random_split(dataset, [n, len(dataset)-n])
#     train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
#     val_loader   = DataLoader(val_ds,   batch_size=args.batch_size, shuffle=False)

#     # 2) Models
#     lstm = LSTMGaussianPredictor(
#         in_size=2,     # x,y only
#         hidden_size=args.hidden,
#         num_layers=args.layers
#     ).to(device)

#     risk = RiskModel(
#         intended_path_csv=args.intended_path,
#         feasible_region_npy=args.feasible,
#         A=args.A, alpha=args.alpha,
#         B=args.B,
#         C=args.C, beta=args.beta, gamma=args.gamma,
#         D=args.D, k=args.k,
#         E=args.E
#     ).to(device)

#     optimizer = optim.Adam(
#         list(lstm.parameters()) + list(risk.parameters()),
#         lr=args.lr
#     )

#     best_ade = float('inf')

#     # 3) Training loop
#     for epoch in range(1, args.epochs+1):
#         print("LSTM and RISK train")
#         lstm.train(); risk.train()
#         total_loss = 0.0
#         print('Epoch:',epoch)

#         for batch in train_loader:
#             # obs, pred_gt, meta = batch
#             # # obs: (B, obs_len, D), pred_gt: (B, pred_len, D)
#             # B = obs.size(0)
#             # obs = obs.to(device)  # D includes ped(2),speed,AV...
            
#             # # we only need ped (x,y) from obs; dataset encodes full state in tuple
#             # ped_hist = obs[..., :2]  # (B, obs_len, 2)

#             # # AV features and gaze:
#             # av_feats = meta['df']    # you might need to extract columns from df
#             # # For simplicity assume av_feats dict of Tensors (B,)
#             # #    'AV_pos','v_AV','a_AV','b_type','b_intensity','s_stop' 
#             # av_dict = meta['av_next']  
#             # TAV = meta['TAV'].to(device)



#             obs, pred_gt, meta = batch
#             obs = obs.to(device)                    # (B, obs_len, D)
#             pred_gt = pred_gt.to(device)            # (B, pred_len, D)

#         # --- extract just the pedestrian (x,y) history ---
#             ped_hist = obs[..., :2]                 # (B, obs_len, 2)

#             # --- build the AV‐features dict for step t=0 ---
#             av_dict = {
#                 'AV_pos':     meta['AV_pos'].to(device),      # (B,2)
#                 'v_AV':       meta['v_AV'].to(device),        # (B,)
#                 'a_AV':       meta['a_AV'].to(device),        # (B,)
#                 'b_type':     meta['b_type'].to(device),      # (B,)
#                 'b_intensity':meta['b_intensity'].to(device), # (B,)
#                 's_stop':     meta['s_stop'].to(device)       # (B,)
#             }
#             gaze_time = meta['TAV'].to(device)  

#             # initialize loss for this batch
#             batch_loss = 0.0

#             # rolling window: start with obs window
#             hist = ped_hist.clone()

#             # For each prediction step t = 0...pred_len-1
#             for t in range(args.pred_len):
#                 # 1) LSTM → μ_t, log_var_t
#                 mu, log_var = lstm(hist)

#                 # 2) sample K candidates
#                 x_last = hist[:, -1, :]
#                 cand, _ = sample_candidates_gaussian(mu, log_var, x_last, K=args.K)
#                 # cand: (K,B,2)

#                 # 3) Build action_params (v_ped_next & heading change)
#                 Δt = args.delta_t
#                 vel = torch.norm(cand - x_last.unsqueeze(0), dim=2) / Δt  # (K,B)
#                 dtheta = torch.zeros_like(vel)                            # (K,B)
#                 action_params = torch.stack([vel, dtheta], dim=2)         # (K,B,2)

#                 # 4) Build candidate_states for RiskModel
#                 candidate_states = {}
#                 for key, val in av_dict.items():
#                     candidate_states[key] = val.unsqueeze(0).repeat(args.K,1,*([1]*(val.ndim-1))).to(device)
#                 candidate_states['ped_pos'] = cand

#                 gaze_KB = gaze_time.unsqueeze(0).repeat(args.K,1)  # (K,B)

#                 # 5) Compute risk+cost
#                 R = risk(candidate_states, action_params, gaze_KB)  # (K,B)

#                 # 6) identify the *true* next ped position
#                 true_next = pred_gt[:, t, :2].to(device)  # (B,2)
#                 d2obs = torch.norm(cand - true_next.unsqueeze(0), dim=2)  # (K,B)
#                 idx_obs = torch.argmin(d2obs, dim=0)                       # (B,)

#                 # 7) softmax‐NLL loss
#                 loss_t = softmax_nll(R, idx_obs)
#                 batch_loss += loss_t

#                 # 8) update history: feed true next (teacher forcing)
#                 hist = torch.cat([hist[:,1:,:], true_next.unsqueeze(1)], dim=1)

#             # average loss over pred_len
#             batch_loss = batch_loss / args.pred_len
#             optimizer.zero_grad()
#             batch_loss.backward()
#             optimizer.step()

#             total_loss += batch_loss.item()

#         avg_loss = total_loss / len(train_loader)

#         # 4) Validation: compute ADE/FDE with greedy rollout
#         print(' Validation: compute ADE/FDE with greedy rollout')

#         lstm.eval(); risk.eval()
#         all_pred, all_gt = [], []
#         with torch.no_grad():
#             for obs, pred_gt, meta in val_loader:
#                 B = obs.size(0)
#                 obs = obs.to(device)
#                 hist = obs[..., :2].clone()  # (B, obs_len, 2)

#                 # Build AV dict exactly as in training:
#                 av_dict = {
#                     'AV_pos':      meta['AV_pos'].to(device),
#                     'v_AV':        meta['v_AV'].to(device),
#                     'a_AV':        meta['a_AV'].to(device),
#                     'b_type':      meta['b_type'].to(device),
#                     'b_intensity': meta['b_intensity'].to(device),
#                     's_stop':      meta['s_stop'].to(device)
#                 }
#                 gaze_time = meta['TAV'].to(device)

#                 preds = []
#                 for t in range(args.pred_len):
#                     mu, log_var = lstm(hist)
#                     cand, _ = sample_candidates_gaussian(mu, log_var, hist[:, -1, :], K=args.K)

#                     # Use av_dict above, not meta['av_next']
#                     candidate_states = {k: v.unsqueeze(0).repeat(args.K,1,*([1]*(v.ndim-1))).to(device)
#                                         for k, v in av_dict.items()}
#                     candidate_states['ped_pos'] = cand

#                     gaze_KB = gaze_time.unsqueeze(0).repeat(args.K, 1)
#                     vel = torch.norm(cand - hist[:, -1, :].unsqueeze(0), dim=2) / args.delta_t
#                     action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)

#                     R = risk(candidate_states, action_params, gaze_KB)
#                     idx = torch.argmin(R, dim=0)
#                     next_pred = cand[idx, torch.arange(B)]
#                     preds.append(next_pred.cpu().numpy())
#                     hist = torch.cat([hist[:,1:,:], next_pred.unsqueeze(1)], dim=1)

#                 all_pred.append(np.stack(preds, axis=1))  # (B, pred_len,2)
#                 all_gt.append(pred_gt[..., :2].numpy())  # (B, pred_len,2)

#         all_pred = np.concatenate(all_pred, axis=0)
#         all_gt   = np.concatenate(all_gt, axis=0)
#         ADE, FDE = compute_ade_fde(all_gt, all_pred)






#         # lstm.eval(); risk.eval()
#         # all_pred, all_gt = [], []
#         # with torch.no_grad():
#         #     for batch in val_loader:
#         #         obs, pred_gt, meta = batch
#         #         B = obs.size(0)
#         #         obs = obs.to(device)
#         #         hist = obs[..., :2].clone()  # (B, obs_len,2)

#         #         preds = []
#         #         for t in range(args.pred_len):
#         #             mu, log_var = lstm(hist)
#         #             cand, _ = sample_candidates_gaussian(mu, log_var, hist[:, -1, :], K=args.K)
#         #             # risk scoring...
#         #             av_dict = meta['AV_pos']
#         #             candidate_states = {'ped_pos': cand}
#         #             for key, val in av_dict.items():
#         #                 candidate_states[key] = val.unsqueeze(0).repeat(args.K,1,*([1]*(val.ndim-1))).to(device)
#         #             gaze_KB = meta['TAV'].unsqueeze(0).repeat(args.K,1).to(device)
#         #             vel = torch.norm(cand - hist[:, -1, :].unsqueeze(0), dim=2)/args.delta_t
#         #             action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)
#         #             R = risk(candidate_states, action_params, gaze_KB)
#         #             idx = torch.argmin(R, dim=0)
#         #             next_pred = cand[idx, torch.arange(B)]
#         #             preds.append(next_pred.cpu().numpy())
#         #             hist = torch.cat([hist[:,1:,:], next_pred.unsqueeze(1)], dim=1)

#         #         preds = np.stack(preds, axis=1)   # (B, pred_len, 2)
#         #         gt    = pred_gt[..., :2].numpy()  # (B, pred_len, 2)
#         #         all_pred.append(preds)
#         #         all_gt.append(gt)

#         # all_pred = np.concatenate(all_pred, axis=0)
#         # all_gt   = np.concatenate(all_gt, axis=0)
#         # ADE, FDE = compute_ade_fde(all_gt, all_pred)

#         # Save best
#         if ADE < best_ade:
#             best_ade = ADE
#             torch.save({'lstm':lstm.state_dict(), 'risk':risk.state_dict(),
#                         'optim':optimizer.state_dict(), 'epoch':epoch},
#                        args.checkpoint)
#             # torch.save({
#             #     'lstm': lstm.state_dict(),
#             #     'risk': risk.state_dict()
#             # }, args.checkpoint)

#         if epoch % 5 == 0:
#             print(f"[{epoch:03d}] TrainLoss={avg_loss:.4f}  ADE={ADE:.4f}  FDE={FDE:.4f}")

#     print("Training complete. Best ADE:", best_ade)


# if __name__ == "__main__":
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--data_dir',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
#     parser.add_argument('--intended_path',  type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
#     parser.add_argument('--feasible',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
#     parser.add_argument('--obs_len',        type=int, default=10)
#     parser.add_argument('--pred_len',       type=int, default=10)
#     parser.add_argument('--train_frac',     type=float, default=0.8)
#     parser.add_argument('--batch_size',     type=int, default=16)
#     parser.add_argument('--hidden',         type=int, default=64)
#     parser.add_argument('--layers',         type=int, default=2)
#     parser.add_argument('--K',              type=int, default=5)
#     parser.add_argument('--delta_t',        type=float, default=0.1)
#     parser.add_argument('--lr',             type=float, default=1e-4)
#     parser.add_argument('--epochs',         type=int, default=50)
#     parser.add_argument('--A', type=float, default=1.0)
#     parser.add_argument('--alpha', type=float, default=1.0)
#     parser.add_argument('--B', type=float, default=1.0)
#     parser.add_argument('--C', type=float, default=1.0)
#     parser.add_argument('--beta', type=float, default=1.0)
#     parser.add_argument('--gamma', type=float, default=1.0)
#     parser.add_argument('--D', type=float, default=1.0)
#     parser.add_argument('--k', type=float, default=1.0)
#     parser.add_argument('--E', type=float, default=1.0)
#     parser.add_argument('--checkpoint', type=str, default='best_model.pt')
#     args = parser.parse_args()

#     train_all(args)


# train_all.py

# import os
# import math
# import argparse
# import torch
# import torch.nn as nn
# import torch.optim as optim
# import numpy as np
# from torch.utils.data import DataLoader
# from sklearn.metrics import mean_squared_error
# import matplotlib.pyplot as plt

# from vr_dataset import VRPedestrianDataset
# from pred_lstm import LSTMGaussianPredictor
# from risk_model import RiskModel
# from sampler import sample_candidates_gaussian

# def softmax_nll(R, idx_obs):
#     R_max = R.min(dim=0, keepdim=True)[0]
#     R_obs = R[idx_obs, torch.arange(R.shape[1])]
#     logZ = R_max.squeeze(0) + torch.log((R_max - R).exp().sum(dim=0) + 1e-6)
#     return torch.mean(R_obs + logZ)

# def compute_ade_fde(gt, pred):
#     diffs = gt - pred
#     dists = np.linalg.norm(diffs, axis=2)
#     return dists.mean(), dists[:, -1].mean()

# def train_risk(args):
#     device = 'cuda' if torch.cuda.is_available() else 'cpu'

#     dataset = VRPedestrianDataset(
#         data_path=args.data_dir,
#         obs_len=args.obs_len,
#         pred_len=args.pred_len
#     )
#     print("Dataloaded")
#     n = int(len(dataset) * args.train_frac)
#     train_ds, val_ds = torch.utils.data.random_split(dataset, [n, len(dataset) - n])
#     train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
#     val_loader = DataLoader(val_ds, batch_size=args.batch_size, shuffle=False)

#     # Load pretrained LSTM
#     lstm = LSTMGaussianPredictor(in_size=3, hidden_size=args.hidden, num_layers=args.layers).to(device)
#     lstm.load_state_dict(torch.load(args.pretrained_lstm, map_location=device))
#     lstm.eval()
#     for p in lstm.parameters(): p.requires_grad = False

#     risk = RiskModel(
#         intended_path_csv=args.intended_path,
#         feasible_region_npy=args.feasible,
#         A=args.A, alpha=args.alpha,
#         B=args.B, C=args.C, beta=args.beta, gamma=args.gamma,
#         D=args.D, k=args.k, E=args.E
#     ).to(device)

#     optimizer = optim.Adam(risk.parameters(), lr=args.lr)
#     best_ade = float('inf')
#     losses, ADEs, FDEs = [], [], []

#     for epoch in range(1, args.epochs + 1):
#         risk.train()
#         total_loss = 0.0

#         for obs, pred_gt, meta in train_loader:
#             obs, pred_gt = obs.to(device), pred_gt.to(device)
#             hist = obs[..., :3]
#             x_last = hist[:, -1, :2]
#             v_last = hist[:, -1, 2:3]

#             av_dict = {k: meta[k].to(device) for k in ['AV_pos','v_AV','a_AV','b_type','b_intensity','s_stop']}
#             gaze = meta['TAV'].to(device)

#             batch_loss = None
#             for t in range(args.pred_len):
#                 with torch.no_grad():
#                     mu_dxdy, mu_speed, log_var = lstm(hist)
#                 log_var_xy = log_var[:, :2]
#                 log_var_sp = log_var[:, 2:3]

#                 cand_pos, cand_spd, _ = sample_candidates_gaussian(
#                     mu_dxdy, mu_speed, log_var_xy, log_var_sp,
#                     K=args.K
#                 )  # returns (K,B,2),(K,B,1)

#                 # assemble action_params using sampled speeds as velocity
#                 vel = cand_spd.squeeze(-1)  # (K,B)
#                 action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)

#                 candidate_states = {k: v.unsqueeze(0).repeat(args.K,1,*([1]*(v.ndim-1)))
#                                     for k,v in av_dict.items()}
#                 candidate_states['ped_pos'] = cand_pos
#                 gaze_KB = gaze.unsqueeze(0).repeat(args.K,1)

#                 R_raw = risk(candidate_states, action_params, gaze_KB)
#                 R = (R_raw - R_raw.mean())/(R_raw.std()+1e-6)
#                 if torch.isnan(R).any(): continue

#                 true_next = pred_gt[:, t, :2]
#                 d2 = torch.norm(cand_pos - true_next.unsqueeze(0), dim=2)
#                 idx_obs = torch.argmin(d2, dim=0)
#                 loss_t = softmax_nll(R, idx_obs)

#                 if torch.isfinite(loss_t):
#                     batch_loss = loss_t if batch_loss is None else batch_loss + loss_t
#                     # teacher forcing next hist includes true speed
#                     next_full = torch.cat([true_next, pred_gt[:, t, 2:3]], dim=1)
#                     hist = torch.cat([hist[:,1:,:], next_full.unsqueeze(1)], dim=1)

#             if batch_loss is not None:
#                 batch_loss = batch_loss / args.pred_len
#                 optimizer.zero_grad()
#                 batch_loss.backward()
#                 optimizer.step()
#                 total_loss += batch_loss.item()

#         avg_loss = total_loss / len(train_loader)
#         losses.append(avg_loss)

#         # validation
#         risk.eval()
#         all_pred, all_gt = [], []
#         with torch.no_grad():
            
#             for obs, pred_gt, meta in val_loader:
#                 obs = obs.to(device)
#                 hist = obs[..., :3]
#                 x_last = hist[:, -1, :2]
#                 v_last = hist[:, -1, 2:3]
#                 av_dict = {k: meta[k].to(device) for k in ['AV_pos','v_AV','a_AV','b_type','b_intensity','s_stop']}
#                 gaze = meta['TAV'].to(device)
#                 preds = []
#                 for t in range(args.pred_len):
#                     mu_dxdy, mu_speed, log_var = lstm(hist)
#                     log_var_xy = log_var[:, :2]; log_var_sp = log_var[:, 2:3]
#                     cand_pos, cand_spd, _ = sample_candidates_gaussian(
#                         mu_dxdy, mu_speed, log_var_xy, log_var_sp, K=args.K
#                     )
#                     vel = cand_spd.squeeze(-1)
#                     action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)
#                     candidate_states = {k: v.unsqueeze(0).repeat(args.K,1,*([1]*(v.ndim-1)))
#                                         for k,v in av_dict.items()}
#                     candidate_states['ped_pos'] = cand_pos
#                     gaze_KB = gaze.unsqueeze(0).repeat(args.K,1)
#                     R = risk(candidate_states, action_params, gaze_KB)
#                     idx = torch.argmin(R, dim=0)
#                     nxt = cand_pos[idx, torch.arange(obs.size(0))]
#                     preds.append(nxt.cpu().numpy())
#                     next_full = torch.cat([nxt, torch.zeros_like(nxt[:,:1])], dim=1)
#                     hist = torch.cat([hist[:,1:,:], next_full.unsqueeze(1)], dim=1)
#                 all_pred.append(np.stack(preds, axis=1))
#                 all_gt.append(pred_gt[..., :2].cpu().numpy())

#         all_pred = np.concatenate(all_pred)
#         all_gt = np.concatenate(all_gt)
#         ADE, FDE = compute_ade_fde(all_gt, all_pred)
#         ADEs.append(ADE); FDEs.append(FDE)

#         if ADE < best_ade:
#             best_ade = ADE
#             torch.save(risk.state_dict(), args.checkpoint)

#         print(f"Epoch {epoch:03d} | Loss: {avg_loss:.4f} | ADE: {ADE:.4f} | FDE: {FDE:.4f}")

#     # plot
#     plt.figure(); plt.plot(losses, label='Loss')
#     plt.plot(ADEs, label='ADE'); plt.plot(FDEs, label='FDE')
#     plt.legend(); plt.savefig('risk_training_metrics.png')



# if __name__ == "__main__":
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--data_dir', type=str,default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
#     parser.add_argument('--pretrained_lstm', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/pretrained_lstm_speed.pt')
#     parser.add_argument('--intended_path', type=str,default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
#     parser.add_argument('--feasible', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
#     parser.add_argument('--obs_len', type=int, default=10)
#     parser.add_argument('--pred_len', type=int, default=10)
#     parser.add_argument('--train_frac', type=float, default=0.8)
#     parser.add_argument('--batch_size', type=int, default=1)
#     parser.add_argument('--hidden', type=int, default=64)
#     parser.add_argument('--layers', type=int, default=2)
#     parser.add_argument('--K', type=int, default=5)
#     parser.add_argument('--delta_t', type=float, default=1)
#     parser.add_argument('--lr', type=float, default=1e-4)
#     parser.add_argument('--epochs', type=int, default=30)
#     parser.add_argument('--A', type=float, default=1.0)
#     parser.add_argument('--alpha', type=float, default=1.0)
#     parser.add_argument('--B', type=float, default=1.0)
#     parser.add_argument('--C', type=float, default=1.0)
#     parser.add_argument('--beta', type=float, default=1.0)
#     parser.add_argument('--gamma', type=float, default=1.0)
#     parser.add_argument('--D', type=float, default=1.0)
#     parser.add_argument('--k', type=float, default=1.0)
#     parser.add_argument('--E', type=float, default=1.0)
#     parser.add_argument('--checkpoint', type=str, default='risky_trained.pt')
#     args = parser.parse_args()

#     train_risk(args)



#     parser.add_argument('--data_dir',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
#     parser.add_argument('--intended_path',  type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
#     parser.add_argument('--feasible',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
    



# train_all.py

import os
import csv
import argparse
import math
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from torch.utils.data import DataLoader, random_split
from sklearn.metrics import mean_squared_error

from vr_dataset import VRPedestrianDataset
from pred_lstm import LSTMGaussianPredictor
from risk_model import RiskModel
from sampler import sample_candidates_gaussian
from visualize_trajectories import (
    visualize_batch_rollout,
    prepare_candidate_batch,
    compute_trajectory_smoothness
)


def softmax_nll(R, idx_obs):
    # R: (K, B) trajectory‐level risk sums
    R = (R - R.mean()) / (R.std() + 1e-6)
    R_obs = R[idx_obs, torch.arange(R.shape[1])]
    logZ = torch.logsumexp(-R, dim=0)
    return torch.mean(R_obs + logZ)


def compute_ade_fde(gt, pred):
    # gt,pred: numpy arrays (N, T_pred, 2)
    diffs = gt - pred
    dists = np.linalg.norm(diffs, axis=2)
    return dists.mean(), dists[:, -1].mean()


def train_all(args):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # ─── DATA ───
    dataset = VRPedestrianDataset(
        args.data_dir,
        obs_len=args.obs_len,
        pred_len=args.pred_len
    )
    n_train = int(len(dataset) * args.train_frac)
    train_ds, val_ds = random_split(dataset, [n_train, len(dataset) - n_train])
    train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
    val_loader   = DataLoader(val_ds,   batch_size=args.batch_size, shuffle=False)

    # ─── MODELS ───
    # Load pretrained LSTM (frozen)
    lstm = LSTMGaussianPredictor(
        in_size=3,
        hidden_size=args.hidden,
        num_layers=args.layers
    ).to(device)
    lstm.load_state_dict(torch.load(args.pretrained_lstm, map_location=device))
    lstm.eval()
    for p in lstm.parameters():
        p.requires_grad = False

    # Risk model to train
    risk = RiskModel(
        intended_path_csv=args.intended_path,
        feasible_region_npy=args.feasible,
        A=args.A, alpha=args.alpha,
        B=args.B,
        C=args.C, beta=args.beta, gamma=args.gamma,
        D=args.D, k=args.k,
        E=args.E
    ).to(device)

    optimizer = optim.Adam(risk.parameters(), lr=args.lr)

    # ─── LOGGING ───
    run_dir = os.path.join(args.save_dir, f"run_{datetime.now():%Y%m%d_%H%M%S}")
    os.makedirs(run_dir, exist_ok=True)
    viz_dir = os.path.join(run_dir, "viz"); os.makedirs(viz_dir, exist_ok=True)
    log_csv = os.path.join(run_dir, "train_log.csv")
    ckpt    = os.path.join(run_dir, "latest.pt")

    # Write CSV header
    with open(log_csv, "w", newline="") as f:
        csv.writer(f).writerow(["epoch","train_loss","val_loss","ADE","FDE"])

    # Try to resume
    start_epoch, best_val_loss, wait = 1, float("inf"), 0
    if os.path.isfile(ckpt):
        ck = torch.load(ckpt, map_location=device)
        risk.load_state_dict(ck["model"])
        optimizer.load_state_dict(ck["opt"])
        start_epoch = ck["epoch"] + 1
        print(f"Resuming from epoch {start_epoch}")

    # Preload for viz
    feasible_np = np.load(args.feasible)
    intended_df = pd.read_csv(args.intended_path)
    intended_np = intended_df[["x","y"]].values

    # Metrics history
    epochs, trains, vals, ADEs, FDEs = [], [], [], [], []

    for epoch in range(start_epoch, args.epochs + 1):
        # ─── TRAIN ───
        risk.train()
        running_loss = 0.0

        for obs, pred_gt, meta in train_loader:
            obs, pred_gt = obs.to(device), pred_gt.to(device)
            B = obs.size(0)

            # Build K‐copies of the observed history: (K,B,T_obs,3)
            hist = obs[..., :3]  # (B, T_obs, 3)
            hist_k = hist.unsqueeze(0).expand(args.K, -1, -1, -1).contiguous()

            # AV features + gaze
            av = {k: meta[k].to(device) for k in [
                "AV_pos","v_AV","a_AV","b_type","b_intensity","s_stop","TAV"
            ]}

            total_risk = torch.zeros(args.K, B, device=device)
            cand_trajs = []

            # roll out K candidates for pred_len steps
            for t in range(args.pred_len):
                # 1) LSTM on flattened history
                flat      = hist_k.view(-1, hist_k.size(2), 3)     # (K*B, T_obs, 3)
                mu_dxdy, mu_speed, log_var = lstm(flat)            # each (K*B, ?)
                mu_dxdy  = mu_dxdy.view(args.K, B, 2)              # (K,B,2)
                mu_speed = mu_speed.view(args.K, B, 1)             # (K,B,1)
                log_var  = log_var.view(args.K, B, 3)              # (K,B,3)

                # # 2) split variances for xy vs speed
                # log_var_xy = log_var[..., :2]   # (K,B,2)
                # log_var_sp = log_var[..., 2:]   # (K,B,1)

                # 3) last positions
                x_last = hist_k[..., -1, :2]    # (K,B,2)

                # 4) sample K next‐step (pos+speed)
                cand_pos, cand_spd = sample_candidates_gaussian(
                    mu_dxdy, mu_speed,
                    log_var,
                    x_last,
                    delta_t=None
                )
                # → cand_pos: (K,B,2), cand_spd: (K,B,1)

                cand_trajs.append(cand_pos)

                # 5) action params from sampled speeds
                vel           = cand_spd.squeeze(-1)                              # (K,B)
                action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)  # (K,B,2)

                # 6) replicate AV features
                cand_states = {
                    k: v.unsqueeze(0).expand(args.K, *v.shape)
                    for k,v in av.items()
                }
                cand_states["ped_pos"] = cand_pos  # (K,B,2)

                # 7) risk on each candidate
                R_step = risk(cand_states, action_params, av["TAV"])  # (K,B)
                total_risk += R_step

                # 8) update hist_k with the sampled full (x,y,speed)
                nxt_full = torch.cat([cand_pos, cand_spd], dim=2)      # (K,B,3)
                hist_k   = torch.cat([
                    hist_k[:, :, 1:, :],       # drop oldest → (K,B,T_obs-1,3)
                    nxt_full.unsqueeze(2)      # add new step → (K,B,1,     3)
                ], dim=2)                       # back to (K,B,T_obs,3)

            # stack cand_trajs → (K,B,pred_len,2)
            cands = torch.stack(cand_trajs, dim=2)

            # pick idx_obs per sample by full‐traj L2
            gt_xy   = pred_gt[..., :2]                                   # (B, T_pred,2)
            gt_xy   = gt_xy.unsqueeze(0).expand(args.K, *gt_xy.shape)    # (K,B,T_pred,2)
            dists   = torch.norm(cands - gt_xy, dim=3).sum(dim=2)         # (K,B)
            idx_obs = torch.argmin(dists, dim=0)                         # (B,)

            # softmax‐NLL over total_risk
            loss = softmax_nll(total_risk, idx_obs)

            # smoothness penalty
            smooth = args.smoothness_lambda * compute_trajectory_smoothness(
                cands,
                hist_k[..., -1, :2]   # last observations (K,B,2)
            )
            loss = loss + smooth

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            running_loss += loss.item()

        train_loss = running_loss / len(train_loader)

        # ─── VALIDATION ───
        risk.eval()
        val_loss = 0.0
        all_preds, all_gts, all_obs, all_av, all_cands = [], [], [], [], []

        with torch.no_grad():
            for obs, pred_gt, meta in val_loader:
                obs, pred_gt = obs.to(device), pred_gt.to(device)
                B = obs.size(0)

                # same rollout as above
                hist_k = obs[..., :3].unsqueeze(0).expand(args.K, -1, -1, -1).contiguous()
                av     = {k: meta[k].to(device) for k in av.keys()}
                total_risk = torch.zeros(args.K, B, device=device)
                cands = []

                for t in range(args.pred_len):
                    flat      = hist_k.view(-1, hist_k.size(2), 3)
                    mu_dxdy, mu_speed, log_var = lstm(flat)
                    mu_dxdy  = mu_dxdy.view(args.K, B, 2)
                    mu_speed = mu_speed.view(args.K, B, 1)
                    log_var  = log_var.view(args.K, B, 3)

                    # split & sample
                    # log_var_xy = log_var[..., :2]
                    # log_var_sp = log_var[..., 2:]
                    x_last     = hist_k[..., -1, :2]
                    cand_pos, cand_spd = sample_candidates_gaussian(
                        mu_dxdy, mu_speed,
                        log_var,
                        x_last,
                        delta_t=None
                    )
                    cands.append(cand_pos)

                    vel           = cand_spd.squeeze(-1)
                    action_params = torch.stack([vel, torch.zeros_like(vel)], dim=2)
                    cand_states   = {
                        k: v.unsqueeze(0).expand(args.K, *v.shape)
                        for k, v in av.items()
                    }
                    cand_states["ped_pos"] = cand_pos
                    R_step = risk(cand_states, action_params, av["TAV"])
                    total_risk += R_step

                    # # greedy update for hist
                    # idx     = torch.argmin(R_step, dim=0)               # (B,)
                    # nxt_xy  = cand_pos[idx, torch.arange(B)]            # (B,2)
                    # nxt_spd = cand_spd[idx, torch.arange(B)]            # (B,1)
                    # nxt_full= torch.cat([nxt_xy, nxt_spd], dim=1)       # (B,3)
                    # hist    = hist[:, 1:, :].to(device)                 # (B,T_obs-1,3)
                    # # hist    = torch.cat([hist, nxt_full.unsqueeze(1)], dim=1)  # (B,T_obs,3)


                     # greedy update
                    idx_min = torch.argmin(R_step, dim=0)
                    nxt_xy  = cand_pos[idx_min, torch.arange(B)]
                    nxt_spd = cand_spd[idx_min, torch.arange(B)]
                    nxt_full= torch.cat([nxt_xy, nxt_spd], dim=1)
                    # hist = torch.cat([hist[:,1:,:], nxt_full.unsqueeze(1)], dim=1)
                    nxt_full_k = torch.cat([cand_pos, cand_spd], dim=2).unsqueeze(2)
                    hist_k = torch.cat([hist_k[:,:,1:,:], nxt_full_k], dim=2)

                    # # also update hist_k
                    # nxt_full_k = torch.cat([cand_pos, cand_spd], dim=2)    # (K,B,3)
                    # hist_k     = torch.cat([
                    #     hist_k[:, :, 1:, :],
                    #     nxt_full_k.unsqueeze(2)
                    # ], dim=2)

                # pick best full‐traj
                cands_full = torch.stack(cands, dim=2)  # (K,B,pred_len,2)
                idx_full   = torch.argmin(total_risk, dim=0)
                pred_seq   = torch.stack([
                    cands_full[idx_full[b], b] for b in range(B)
                ], dim=0)                             # (B,pred_len,2)

                # val loss
                gt_xy   = pred_gt[..., :2]
                gt_xy   = gt_xy.unsqueeze(0).expand(args.K, *gt_xy.shape)
                dists   = torch.norm(cands_full - gt_xy, dim=3).sum(dim=2)
                idx_obs = torch.argmin(dists, dim=0)
                val_loss += softmax_nll(total_risk, idx_obs).item()

                # collect for viz
                all_preds.append(pred_seq.cpu().numpy())
                all_gts.append(pred_gt[..., :2].cpu().numpy())
                all_obs.append(obs[..., :2].cpu().numpy())
                all_av.append(av["AV_pos"].cpu().numpy())
                all_cands.append(prepare_candidate_batch(cands))

        # finalize metrics
        preds_np = np.concatenate(all_preds, axis=0)
        gts_np   = np.concatenate(all_gts,   axis=0)
        obs_np   = np.concatenate(all_obs,   axis=0)
        av_np    = np.concatenate(all_av,    axis=0)
        cand_np  = torch.concatenate(all_cands, axis=0)
        # cand_np = np.concatenate(
        #             [c.detach().cpu().numpy() for c in all_cands],
        #             axis=0
        #         )

        ade, fde = compute_ade_fde(gts_np, preds_np)
        val_loss  = val_loss / len(val_loader)

        # viz first 8
        visualize_batch_rollout(
            obs_np[:8], gts_np[:8], preds_np[:8], cand_np[:8],
            output_dir=os.path.join(viz_dir, f"epoch{epoch:03d}"),
            av_positions=av_np[:8],
            feasible_poly=feasible_np,
            intended_path=intended_np
        )

        # log + checkpoint
        with open(log_csv, "a", newline="") as f:
            csv.writer(f).writerow([epoch, train_loss, val_loss, ade, fde])

        print(f"[Epoch {epoch:02d}] train={train_loss:.4f}  val={val_loss:.4f}  ADE={ade:.4f}  FDE={fde:.4f}")

        torch.save({
            "epoch": epoch,
            "model": risk.state_dict(),
            "opt":   optimizer.state_dict()
        }, ckpt)

        epochs.append(epoch)
        trains.append(train_loss)
        vals.append(val_loss)
        ADEs.append(ade)
        FDEs.append(fde)

        # early stop
        if val_loss < best_val_loss:
            best_val_loss, wait = val_loss, 0
        else:
            wait += 1
            if wait >= args.patience:
                print("Early stopping.")
                break

    # final metrics plot
    plt.figure()
    plt.plot(epochs, trains, label="train loss")
    plt.plot(epochs, vals,   label="val loss")
    plt.plot(epochs, ADEs,   label="ADE")
    plt.plot(epochs, FDEs,   label="FDE")
    plt.legend(); plt.xlabel("Epoch")
    plt.savefig(os.path.join(run_dir, "metrics.png"))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_dir',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/sample_data/')
    parser.add_argument('--intended_path',  type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
    parser.add_argument('--feasible',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
    parser.add_argument('--pretrained_lstm', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/pretrained_lstm_speed.pt')
    parser.add_argument('--obs_len',         type=int,   default=10)
    parser.add_argument('--pred_len',        type=int,   default=10)
    parser.add_argument('--train_frac',      type=float, default=0.8)
    parser.add_argument('--batch_size',      type=int,   default=32)
    parser.add_argument('--hidden',          type=int,   default=64)
    parser.add_argument('--layers',          type=int,   default=2)
    parser.add_argument('--K',               type=int,   default=10)
    parser.add_argument('--delta_t',         type=float, default=0.1)
    parser.add_argument('--lr',              type=float, default=1e-5)
    parser.add_argument('--epochs',          type=int,   default=50)
    parser.add_argument('--patience',        type=int,   default=10)
    parser.add_argument('--A',               type=float, default=1.0)
    parser.add_argument('--alpha',           type=float, default=1.0)
    parser.add_argument('--B',               type=float, default=1.0)
    parser.add_argument('--C',               type=float, default=1.0)
    parser.add_argument('--beta',            type=float, default=1.0)
    parser.add_argument('--gamma',           type=float, default=1.0)
    parser.add_argument('--D',               type=float, default=1.0)
    parser.add_argument('--k',               type=float, default=1.0)
    parser.add_argument('--E',               type=float, default=1.0)
    parser.add_argument('--save_dir',        type=str,   default='./risk_model_ckpts')
    parser.add_argument('--smoothness_lambda', type=float, default=0.5)
    args = parser.parse_args()

    train_all(args)



#     parser.add_argument('--data_dir',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
#     parser.add_argument('--intended_path',  type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
#     parser.add_argument('--feasible',       type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
    # parser.add_argument('--pretrained_lstm', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/pretrained_lstm_speed.pt')