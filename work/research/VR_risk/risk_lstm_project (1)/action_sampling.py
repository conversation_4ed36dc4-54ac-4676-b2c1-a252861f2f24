import torch
import torch.nn as nn
import torch.nn.functional as F

# ─────────────────────────────────────────────────────────────────────────────
# 1) Define a simple Pedestrian LSTM Predictor
# ─────────────────────────────────────────────────────────────────────────────

class PedLSTM(nn.Module):
    """
    A minimal LSTM-based predictor that takes a history of pedestrian positions
    and outputs a mean next-offset (Δx, Δy).
    """

    def __init__(self, input_size=2, hidden_size=64, num_layers=1):
        """
        Args:
            input_size:  dimensionality of each input step (here, 2 for x,y).
            hidden_size: number of LSTM hidden units.
            num_layers:  number of stacked LSTM layers.
        """
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM: input_size → hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)

        # Linear layer: hidden_size → 2 (predict Δx, Δy)
        self.fc = nn.Linear(hidden_size, 2)

    def forward(self, x_hist):
        """
        Args:
            x_hist: Tensor of shape (batch_size, seq_len, 2), 
                    containing past pedestrian (x,y) positions for the last T steps.

        Returns:
            mu_next: Tensor of shape (batch_size, 2), the predicted mean offset Δx, Δy.
        """
        # Initialize hidden and cell states to zeros
        batch_size = x_hist.size(0)
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size, device=x_hist.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size, device=x_hist.device)

        # Pass through LSTM
        out, (hn, cn) = self.lstm(x_hist, (h0, c0))  # out: (batch_size, seq_len, hidden_size)

        # Take final time-step's hidden feature: out[:, -1, :]
        h_last = out[:, -1, :]  # (batch_size, hidden_size)

        # Predict Δx, Δy
        mu_next = self.fc(h_last)  # (batch_size, 2)

        return mu_next


# ─────────────────────────────────────────────────────────────────────────────
# 2) Action Sampling Procedure
# ─────────────────────────────────────────────────────────────────────────────

def action_sampling_step(ped_lstm, x_hist, x_next_obs, delta=0.5):
    """
    Given the PedLSTM model, a batch of past positions, and the observed next positions,
    generate a candidate set of K=5 possible next positions around the predicted mean,
    then identify which candidate matches the observed next position.

    Args:
        ped_lstm:     instance of PedLSTM (trained or untrained).
        x_hist:       Tensor of shape (B, T, 2), past pedestrian (x,y) positions.
        x_next_obs:   Tensor of shape (B, 2), ground-truth next pedestrian positions.
        delta:        float, magnitude of spatial perturbation (in meters) for candidate offsets.

    Returns:
        candidates_ped:   Tensor of shape (K, B, 2), each candidate next position.
        action_offsets:   Tensor of shape (K, B, 2), the Δ_j offsets used.
        idx_obs:          LongTensor of shape (B,), index j* of the candidate closest to x_next_obs.
    """
    device = x_hist.device
    batch_size = x_hist.size(0)

    # 2.1) Predict mean next-offset μ_t using the LSTM
    #      We assume the LSTM outputs Δx, Δy relative to the last observed x_hist position.
    mu_next = ped_lstm(x_hist)  # (B, 2)

    # We interpret mu_next as the offset to add to the last position in x_hist:
    ped_last = x_hist[:, -1, :]  # (B, 2)
    mu_next_pos = ped_last + mu_next  # predicted next (x,y) for each sample, shape (B,2)

    # 2.2) Define K=5 perturbation offsets Δ_j ∈ { (0,0), (±δ,0), (0,±δ) }
    offsets = torch.tensor([
        [ 0.0,  0.0],
        [ delta,  0.0],
        [-delta,  0.0],
        [ 0.0,  delta],
        [ 0.0, -delta]
    ], device=device)  # shape (K, 2)
    K = offsets.size(0)

    # 2.3) Expand mu_next_pos to shape (K, B, 2), then add each offset
    #      mu_next_pos        : (B, 2)
    #      mu_next_pos.unsqueeze(0): (1, B, 2)
    #      offsets.unsqueeze(1)    : (K, 1, 2)
    #      → broadcast to (K, B, 2)
    mu_expanded = mu_next_pos.unsqueeze(0).expand(K, batch_size, 2)  # (K, B, 2)
    offset_expanded = offsets.unsqueeze(1).expand(K, batch_size, 2)  # (K, B, 2)

    # 2.4) Candidate next positions: x_j' = mu_next_pos + Δ_j
    candidates_ped = mu_expanded + offset_expanded  # (K, B, 2)

    # 2.5) Identify which candidate matches the observed next position x_next_obs
    #      Compute Euclidean distance between each candidate and x_next_obs
    #      candidates_ped: (K, B, 2)
    #      x_next_obs.unsqueeze(0): (1, B, 2) → broadcast to (K, B, 2)
    obs_expanded = x_next_obs.unsqueeze(0).expand(K, batch_size, 2)  # (K, B, 2)
    dists_to_obs = torch.norm(candidates_ped - obs_expanded, dim=2)  # (K, B)

    # For each batch index i, pick the candidate index j that minimizes distance
    idx_obs = torch.argmin(dists_to_obs, dim=0)  # (B,), values in [0..K-1]

    return candidates_ped, offset_expanded, idx_obs


# ─────────────────────────────────────────────────────────────────────────────
# 3) Demonstration of Usage
# ─────────────────────────────────────────────────────────────────────────────

if __name__ == "__main__":
    # Example hyperparameters
    B = 4    # batch size
    T = 10   # history length
    delta = 0.5  # meters

    # Create a PedLSTM instance (hidden_size, layers can be tuned)
    ped_lstm = PedLSTM(input_size=2, hidden_size=64, num_layers=1)

    # Dummy data for demonstration
    # x_hist: random past positions (B, T, 2)
    x_hist = torch.randn(B, T, 2) * 0.1 + torch.tensor([5.0, 3.0])  # centered around (5,3)
    # Assume the "true" next positions (B, 2)
    x_next_obs = torch.randn(B, 2) * 0.1 + torch.tensor([5.5, 3.0])  # around (5.5,3)

    # Move everything to same device (CPU here)
    device = torch.device('cpu')
    ped_lstm = ped_lstm.to(device)
    x_hist = x_hist.to(device)
    x_next_obs = x_next_obs.to(device)

    # Action sampling step
    candidates_ped, action_offsets, idx_obs = action_sampling_step(
        ped_lstm, x_hist, x_next_obs, delta=delta
    )

    # Prints for clarity
    print("Predicted mean next positions (mu_next_pos) + offsets:")
    for j in range(candidates_ped.size(0)):
        print(f"Candidate {j:>1}:  {candidates_ped[j]}")

    print("\nObserved next positions:")
    print(x_next_obs)

    print("\nIndex of candidate closest to observed next position (per batch):")
    print(idx_obs)  # shape (B,)

    # To see which candidate matches each example:
    for i in range(B):
        print(f"Batch {i}: chosen candidate index = {idx_obs[i].item()}, "
              f"candidate position = {candidates_ped[idx_obs[i], i].tolist()}, "
              f"observed = {x_next_obs[i].tolist()}")
