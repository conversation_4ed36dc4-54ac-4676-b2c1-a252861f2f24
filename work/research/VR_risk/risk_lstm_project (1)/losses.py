
import torch
import math

def bivariate_gaussian_nll(mu, sigma, rho, target):
    mux, muy = mu[:, 0], mu[:, 1]
    sigmax, sigmay = sigma[:, 0], sigma[:, 1]
    rho = rho.squeeze(1)
    x, y = target[:, 0], target[:, 1]

    norm_x = x - mux
    norm_y = y - muy
    z = (norm_x / sigmax) ** 2 + (norm_y / sigmay) ** 2 - 2 * rho * norm_x * norm_y / (sigmax * sigmay)
    denom = 2 * (1 - rho ** 2)
    nll = z / denom + torch.log(2 * math.pi * sigmax * sigmay * torch.sqrt(1 - rho ** 2))
    return nll.mean()
