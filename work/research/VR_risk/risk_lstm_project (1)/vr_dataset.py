import torch
from torch.utils.data import Dataset
import pandas as pd
import ast 
import os
import numpy as np

# class VRPedestrianDataset(Dataset):
#     def __init__(self, data_path, obs_len=10, pred_len=10):
#         self.data_path = data_path
#         self.obs_len = obs_len
#         self.pred_len = pred_len
#         self.seq_len = obs_len + pred_len
#         self.samples = []

#         data = load_data(self.data_path)
#         for trial in data:
#             states = trial['states']  # shape: [T, D]
#             if len(states) >= self.seq_len:
#                 for i in range(len(states) - self.seq_len + 1):
#                     self.samples.append({
#                         'obs': states[i:i + obs_len],
#                         'pred': states[i + obs_len:i + self.seq_len],
#                         'meta': {
#                             'subject_id': trial['subject_id'],
#                             'trial_id': trial['trial_id'],
#                             'CIT': trial['CIT'],
#                             'certainty': trial['certainty'],
#                             'brake_profile': trial['brake_profile'],
#                             'timestamps': trial['timestamps'][i:i + self.seq_len],
#                             'df': trial['df']  # full df in case risk model needs it
#                         }
#                     })
#         # return self.samples
#     def __len__(self):
#         return len(self.samples)

#     def __getitem__(self, idx):
#         # item = self.samples[idx]
#         # obs = torch.tensor(item['obs'], dtype=torch.float32)  # shape: [obs_len, D]
#         # pred = torch.tensor(item['pred'], dtype=torch.float32)  # shape: [pred_len, D]
#         # return obs, pred, item['meta']
#         item = self.samples[idx]

#         # Each element of item['obs'] is a tuple:
#         #   (p_ped: np.array(2,), v_ped: float,
#         #    p_AV: np.array(2,), v_AV: float,
#         #    a_AV: float, b_type: float,
#         #    b_intensity: float, b_stop_dist: float,
#         #    TAV: float)
#         # We need to convert that into a single numeric vector per time-step.

#         def flatten_state(state_tuple):
#             p_ped, v_ped, p_AV, v_AV, a_AV, b_type, b_intensity, b_stop_dist, TAV = state_tuple
#             # concatenate pedestrian pos, AV pos, and all scalars into one 1D array
#             return np.concatenate([
#                 np.asarray(p_ped, dtype=np.float32),
#                 [v_ped],
#                 np.asarray(p_AV, dtype=np.float32),
#                 [v_AV, a_AV, b_type, b_intensity, b_stop_dist, TAV]
#             ], axis=0)

#         # Build obs and pred as numpy arrays, then to torch tensor
#         obs_np = np.stack([flatten_state(s) for s in item['obs']], axis=0)
#         pred_np = np.stack([flatten_state(s) for s in item['pred']], axis=0)

#         obs = torch.from_numpy(obs_np).float()   # shape: [obs_len, D]
#         pred = torch.from_numpy(pred_np).float() # shape: [pred_len, D]
#         return obs, pred, item['meta']


# def load_data(data_dir='.'):
#     """Load and preprocess VR dataset CSV files with interpolation."""
#     csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and 'processed' in f]
#     if not csv_files:
#         raise ValueError("No CSV files found in the directory")
    
#     data = []
#     for csv_file in csv_files:
#         file_path = os.path.join(data_dir, csv_file)
#         df = pd.read_csv(file_path)
        
#         # Parse positions
#         df['ped_position'] = df['ped_position'].apply(lambda x: np.array(eval(x)))
#         df['AV_pos'] = df['AV_pos'].apply(lambda x: np.array(eval(x)))

#         df['AV_acc'] = df['AV_speed'].diff() / df['timestamp'].diff()
#         df['AV_acc'] = df['AV_acc'].fillna(0)
        
        
       
        
#         trials = df.groupby(['subject_id', 'trial_id'])
#         for (subject_id, trial_id), group in trials:
#             group = group.sort_values('timestamp')
#             states = []
         
#             for i in range(len(group) - 1):
#                 row = group.iloc[i]
#                 p_ped = row['ped_position']
#                 v_ped = row['ped_speed']
#                 p_AV = row['AV_pos']
#                 v_AV = row['AV_speed']
#                 a_AV = row['AV_acc']
#                 b_profile = row['AV_Brake_Behaviour']
#                 TAV = row['T_TAV']
                
#                 # Encode braking profile
#                 b_type = 0 if 'AV_DRIVES_THROUGH' in b_profile else 1 if 'BRAKE_LINEAR' in b_profile else 2 if 'BRAKE_2STAGES' in b_profile else 0
#                 b_intensity = 1 if 'STRONG_BRAKE_1ST' in b_profile else 2 if 'STRONG_BRAKE_2ND' in b_profile else 0
#                 b_stop_dist = {'EARLYSTOP': 10.0, 'MEDIUMSTOP': 5.0, 'LATESTOP': 2.0}.get(b_profile.split('_')[-1], 0.0)
                
#                 state = (p_ped, float(v_ped), p_AV, float(v_AV), float(a_AV), float(b_type), float(b_intensity), float(b_stop_dist), float(TAV or 0.0))
#                 # action = group['ped_position'].iloc[i+1] - p_ped
                
#                 states.append(state)
#                 # actions.append(action)
            
#             if states:
#                 data.append({
#                     'subject_id': subject_id,
#                     'trial_id': trial_id,
#                     'states': states,
#                     'CIT': group['CIT'].iloc[0],
#                     'certainty': group['certainty'].iloc[0],
#                     'brake_profile': group['AV_Brake_Behaviour'].iloc[0],
#                     'timestamps': group['timestamp'].values[:-1],
#                     'df': group
#                 })
    
#     return data
# vr_dataset.py (updated)


class VRPedestrianDataset(Dataset):
    def __init__(self, data_path, obs_len=10, pred_len=10):
        self.samples = []
        for fn in os.listdir(data_path):
            if not fn.endswith('.csv'): 
                continue
            df = pd.read_csv(os.path.join(data_path, fn))
            # parse each row’s fields into numpy / Python primitives
            ped_pos = df['ped_position'].apply(ast.literal_eval).tolist()
            ped_speed = df['ped_speed'].values.astype(np.float32)
            av_pos = df['AV_pos'].apply(ast.literal_eval).tolist()
            av_speed = df['AV_speed'].values.astype(np.float32)
            av_accel = (df['AV_speed'].diff() / df['timestamp'].diff()).fillna(0).values.astype(np.float32)
            b_profile = df['AV_Brake_Behaviour'].tolist()
            gaze     = df['T_TAV'].fillna(0).values.astype(np.float32)
            timestamps = df['timestamp'].values.astype(np.float32)

            # encode braking and stop distances
            def encode_brake(bp):
                if 'AV_DRIVES_THROUGH' in bp: return 0, 0, 0.0
                intensity = 1 if 'STRONG_BRAKE_1ST' in bp else 2 if 'STRONG_BRAKE_2ND' in bp else 0
                bp_type = 1 if 'BRAKE_LINEAR' in bp else 2 if 'BRAKE_2STAGES' in bp else 0
                stop_map = {'EARLYSTOP':10.0, 'MEDIUMSTOP':5.0, 'LATESTOP':2.0}
                dist = stop_map.get(bp.split('_')[-1], 0.0)
                return float(bp_type), float(intensity), float(dist)

            full_states = []
            for i in range(len(df)-1):
                bt, bi, sd = encode_brake(b_profile[i])
                full_states.append((
                    np.asarray(ped_pos[i], dtype=np.float32),
                    float(ped_speed[i]),
                    np.asarray(av_pos[i], dtype=np.float32),
                    float(av_speed[i]),
                    float(av_accel[i]),
                    bt, bi, sd,
                    float(gaze[i])
                ))

            # sliding window
            seq_len = obs_len + pred_len
            for i in range(len(full_states) - seq_len + 1):
                obs_seq  = full_states[i : i+obs_len]
                pred_seq = full_states[i+obs_len : i+seq_len]
                # meta‐tensors for *first* pred step
                idx0 = i + obs_len
                meta = {
                    'AV_pos':     torch.tensor(av_pos[idx0], dtype=torch.float32),
                    'v_AV':       torch.tensor(av_speed[idx0], dtype=torch.float32),
                    'a_AV':       torch.tensor(av_accel[idx0], dtype=torch.float32),
                    'time':       torch.tensor(timestamps[idx0], dtype=torch.float32),
                    'b_type':     torch.tensor(encode_brake(b_profile[idx0])[0], dtype=torch.float32),
                    'b_intensity':torch.tensor(encode_brake(b_profile[idx0])[1], dtype=torch.float32),
                    's_stop':     torch.tensor(encode_brake(b_profile[idx0])[2], dtype=torch.float32),
                    'TAV':        torch.tensor(gaze[idx0], dtype=torch.float32)
                   
                }
                self.samples.append({'obs': obs_seq, 'pred': pred_seq, 'meta': meta})

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        item = self.samples[idx]

        def flatten(state):
            p_ped, v_ped, p_AV, v_AV, a_AV, bt, bi, sd, tv = state
            return np.concatenate([p_ped, [v_ped], p_AV, [v_AV, a_AV, bt, bi, sd, tv]], axis=0)

        obs_np  = np.stack([flatten(s) for s in item['obs']], axis=0)
        pred_np = np.stack([flatten(s) for s in item['pred']], axis=0)

        obs  = torch.from_numpy(obs_np).float()   # (obs_len, D)
        pred = torch.from_numpy(pred_np).float()  # (pred_len, D)
        return obs, pred, item['meta']


if __name__ == '__main__':
    dataset = VRPedestrianDataset('/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
    print("hi")