# import os
# import math
# import ast
# import torch
# import torch.nn as nn
# import numpy as np
# import pandas as pd
# import matplotlib.pyplot as plt
# from torch.utils.data import Dataset, DataLoader
# from sklearn.metrics import mean_squared_error

# # # ─────────────────────────────────────────────────────────────────────────────
# # # Dataset & Loader (unchanged)
# # # ─────────────────────────────────────────────────────────────────────────────
# class PedestrianDataset(Dataset):
#     def __init__(self, records, seq_length=10):
#         self.X, self.y = self._prepare(records, seq_length)

#     def _prepare(self, records, seq_length):
#         X, y = [], []
#         for trial in records:
#             states = trial['states']  # list of [x, y, speed]
#             for i in range(len(states) - seq_length):
#                 seq = states[i:i + seq_length]
#                 tgt = states[i + seq_length][:2]  # only position target
#                 X.append(seq)
#                 y.append(tgt)
#         return torch.tensor(X, dtype=torch.float32), torch.tensor(y, dtype=torch.float32)

#     def __len__(self): return len(self.X)
#     def __getitem__(self, i): return self.X[i], self.y[i]

# def load_data_from_csv(data_dir):
#     records = []
#     for fn in sorted(os.listdir(data_dir)):
#         if not fn.endswith('.csv'): continue
#         df = pd.read_csv(os.path.join(data_dir, fn))
#         states = []
#         for pos_str, speed in zip(df['ped_position'], df['ped_speed']):
#             xy = ast.literal_eval(pos_str)
#             states.append([xy[0], xy[1], speed])
#         records.append({'states': states})
#     return records

# def split_records(records, train_frac=0.8):
#     np.random.shuffle(records)
#     n = int(len(records) * train_frac)
#     return records[:n], records[n:]


# # ─────────────────────────────────────────────────────────────────────────────
# # Gaussian LSTM Predictor definition (must match the saved model)
# # ─────────────────────────────────────────────────────────────────────────────
# class LSTMGaussianPredictor(nn.Module):
#     def __init__(self, in_size=3, hidden_size=64, num_layers=2, dropout=0.2):
#         super().__init__()
#         self.lstm = nn.LSTM(in_size, hidden_size, num_layers,
#                             batch_first=True, dropout=dropout)
#         self.head = nn.Linear(hidden_size, 4)  # μₓ, μᵧ, logσₓ², logσᵧ²

#     def forward(self, x):
#         out, _ = self.lstm(x)
#         h = out[:, -1, :]                # last time-step
#         params = self.head(h)            # (B,4)
#         mu = params[:, :2]
#         log_var = params[:, 2:].clamp(min=-10, max=10)
#         return mu, log_var

def gaussian_nll(mu, log_var, target):
    var = torch.exp(log_var)
    diff2 = (target - mu).pow(2)
    nll = 0.5 * (math.log(2*math.pi) + log_var + diff2/var).sum(dim=1)
    return nll.mean()


# # ─────────────────────────────────────────────────────────────────────────────
# # Test Script
# # ─────────────────────────────────────────────────────────────────────────────
# def test_model(data_dir,
#                model_path='best_lstm_gaussian.pth',
#                seq_len=10,
#                batch_size=32,
#                device='cpu'):

#     # 1) Load & split data
#     records = load_data_from_csv(data_dir)
#     _, val_recs = split_records(records)
#     val_ds = PedestrianDataset(val_recs, seq_len)
#     val_dl = DataLoader(val_ds, batch_size, shuffle=False)

#     # 2) Initialize model & load weights
#     model = LSTMGaussianPredictor().to(device)
#     model.load_state_dict(torch.load(model_path, map_location=device))
#     model.eval()

#     # 3) Run through the validation set
#     all_mu = []
#     all_targets = []
#     total_nll = 0.0




#     with torch.no_grad():
#         for Xb, yb in val_dl:
#             Xb = Xb.to(device)
#             yb = yb.to(device)
#             mu, log_var = model(Xb)
#             total_nll += gaussian_nll(mu, log_var, yb).item() * Xb.size(0)
#             all_mu.append(mu.cpu().numpy())
#             all_targets.append(yb.cpu().numpy())

#     # 4) Aggregate metrics
#     total_samples = len(val_ds)
#     val_nll = total_nll / total_samples
#     preds = np.vstack(all_mu)
#     gts   = np.vstack(all_targets)
#     val_rmse = math.sqrt(mean_squared_error(gts, preds))

#     print(f"Validation NLL = {val_nll:.4f}")
#     print(f"Validation RMSE = {val_rmse:.4f}")

#     # 5) Plot results on the first trial in the val set
#     #    Reconstruct one trial’s seq of preds vs actual
#     #    (Optional: you can loop through multiple trials similarly)
#     trial_preds = []
#     trial_actual = []
#     # pick the first record for plotting
#     first_seq = val_recs[0]['states']
#     for i in range(seq_len, len(first_seq)):
#         seq = first_seq[i - seq_len:i]
#         inp = torch.tensor([seq], dtype=torch.float32).to(device)
#         mu, _ = model(inp)
#         trial_preds.append(mu.detach().cpu().numpy()[0]) 
#         trial_actual.append(first_seq[i][:2])
#     trial_preds = np.array(trial_preds)
#     trial_actual = np.array(trial_actual)

#     # Plot X, Y, and Trajectory
#     ts = range(len(trial_actual))
#     plt.figure()
#     plt.plot(ts, trial_actual[:,0], label='Actual X')
#     plt.plot(ts, trial_preds[:,0],   label='Pred X')
#     plt.xlabel('Time Step')
#     plt.ylabel('X')
#     plt.legend()
#     plt.savefig('fatest_x.png')

#     plt.figure()
#     plt.plot(ts, trial_actual[:,1], label='Actual Y')
#     plt.plot(ts, trial_preds[:,1],   label='Pred Y')
#     plt.xlabel('Time Step')
#     plt.ylabel('Y')
#     plt.legend()
#     plt.savefig('fatest_y.png')

#     plt.figure()
#     plt.plot(trial_actual[:,0], trial_actual[:,1], label='Actual Traj')
#     plt.plot(trial_preds[:,0],   trial_preds[:,1],   label='Pred Traj')
#     plt.xlabel('X')
#     plt.ylabel('Y')
#     plt.legend()
#     plt.savefig('fatest_traj.png')

#     print("Saved plots: test_x.png, test_y.png, test_traj.png")

import os
import math
import ast
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import mean_squared_error

# ─────────────────────────────────────────────────────────────────────────────
# Dataset & Loader (includes speed)
# ─────────────────────────────────────────────────────────────────────────────
class PedestrianDataset(Dataset):
    def __init__(self, records, seq_length=10):
        self.X, self.y = self._prepare(records, seq_length)

    def _prepare(self, records, seq_length):
        X, y = [], []
        for trial in records:
            states = trial['states']  # list of [x, y, speed]
            for i in range(len(states) - seq_length):
                seq = states[i:i + seq_length]
                tgt = states[i + seq_length]       # [x, y, speed]
                X.append(seq)
                y.append(tgt)
        return torch.tensor(X, dtype=torch.float32), torch.tensor(y, dtype=torch.float32)

    def __len__(self): return len(self.X)
    def __getitem__(self, i): return self.X[i], self.y[i]



def load_data_from_csv(data_dir):
    records = []
    for fn in sorted(os.listdir(data_dir)):
        if not fn.endswith('.csv'): continue
        df = pd.read_csv(os.path.join(data_dir, fn))
        states = []
        for pos_str, speed in zip(df['ped_position'], df['ped_speed']):
            xy = ast.literal_eval(pos_str)
            states.append([xy[0], xy[1], speed])
        records.append({'states': states})
    return records


def split_records(records, train_frac=0.8):
    np.random.shuffle(records)
    n = int(len(records) * train_frac)
    return records[:n], records[n:]

# ─────────────────────────────────────────────────────────────────────────────
# Gaussian LSTM Predictor (6-dim head)
# ─────────────────────────────────────────────────────────────────────────────
class LSTMGaussianPredictor(nn.Module):
    def __init__(self, in_size=3, hidden_size=64, num_layers=2, dropout=0.2):
        super().__init__()
        self.lstm = nn.LSTM(in_size, hidden_size, num_layers,
                            batch_first=True, dropout=dropout)
        # outputs: absolute x_next, y_next, speed_next; logσx², logσy², logσs²
        self.head = nn.Linear(hidden_size, 6)

    def forward(self, x):
        out, _ = self.lstm(x)
        h = out[:, -1, :]
        params = self.head(h)
        mu_pos   = params[:, :2]
        mu_speed = params[:, 2:3]
        log_var  = params[:, 3:].clamp(min=-10, max=10)
        mu = torch.cat([mu_pos, mu_speed], dim=1)
        return mu, log_var


def gaussian_nll(mu, log_var, target):
    var = torch.exp(log_var)
    diff2 = (target - mu).pow(2)
    return 0.5 * ((math.log(2*math.pi) + log_var) + diff2/var).sum(dim=1).mean()

# ─────────────────────────────────────────────────────────────────────────────
# Test Script (absolute outputs: no addition to x_last/y_last)
# ─────────────────────────────────────────────────────────────────────────────
def test_model(data_dir,
               model_path='pretrained_lstm_speed.pth',
               seq_len=10,
               batch_size=32,
               device='cpu'):

    # 1) Load & split data
    records = load_data_from_csv(data_dir)
    _, val_recs = split_records(records)
    val_ds = PedestrianDataset(val_recs, seq_len)
    val_dl = DataLoader(val_ds, batch_size, shuffle=False)

    # 2) Initialize model & load weights
    model = LSTMGaussianPredictor().to(device)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint)
    model.eval()

    # 3) Evaluate on validation set
    all_mu = []
    all_targets = []
    total_nll = 0.0

    with torch.no_grad():
        for Xb, yb in val_dl:
            Xb, yb = Xb.to(device), yb.to(device)
            mu, log_var = model(Xb)
            total_nll += gaussian_nll(mu, log_var, yb).item() * Xb.size(0)
            all_mu.append(mu.cpu().detach().numpy())
            all_targets.append(yb.cpu().detach().numpy())

    total_samples = len(val_ds)
    val_nll = total_nll / total_samples
    preds = np.vstack(all_mu)
    gts   = np.vstack(all_targets)

    pos_rmse   = math.sqrt(mean_squared_error(gts[:, :2], preds[:, :2]))
    speed_rmse = math.sqrt(mean_squared_error(gts[:, 2], preds[:, 2]))

    print(f"Validation NLL = {val_nll:.4f}")
    print(f"Position RMSE = {pos_rmse:.4f} m  |  Speed RMSE = {speed_rmse:.4f} m/s")

    # 4) Plot first trial rollout (absolute predictions)
    trial_actual = []
    trial_pred = []
    trial_speed_actual = []
    trial_speed_pred = []
    first_seq = val_recs[0]['states']

    for i in range(seq_len, len(first_seq)):
        seq = first_seq[i - seq_len:i]
        inp = torch.tensor([seq], dtype=torch.float32).to(device)
        mu, _ = model(inp)
        x_pred, y_pred, s_pred = mu[0].cpu().detach().numpy()

        trial_actual.append(first_seq[i][:2])
        trial_pred.append([x_pred, y_pred])
        trial_speed_actual.append(first_seq[i][2])
        trial_speed_pred.append(s_pred)

    trial_actual = np.array(trial_actual)
    trial_pred   = np.array(trial_pred)
    ts = range(len(trial_actual))

    # position plots (unchanged)
    plt.figure()
    plt.plot(ts, trial_actual[:,0], label='Actual X')
    plt.plot(ts, trial_pred[:,0],   label='Pred X')
    plt.xlabel('Time Step'); plt.ylabel('X'); plt.legend()
    plt.savefig('fatest_x.png')

    plt.figure()
    plt.plot(ts, trial_actual[:,1], label='Actual Y')
    plt.plot(ts, trial_pred[:,1],   label='Pred Y')
    plt.xlabel('Time Step'); plt.ylabel('Y'); plt.legend()
    plt.savefig('fatest_y.png')

    plt.figure()
    plt.plot(trial_actual[:,0], trial_actual[:,1], label='Actual Traj')
    plt.plot(trial_pred[:,0],   trial_pred[:,1],   label='Pred Traj')
    plt.xlabel('X'); plt.ylabel('Y'); plt.legend()
    plt.savefig('fatest_traj.png')

    # speed plot (added)
    plt.figure()
    plt.plot(ts, trial_speed_actual, label='Actual Speed')
    plt.plot(ts, trial_speed_pred,   label='Pred Speed')
    plt.xlabel('Time Step'); plt.ylabel('Speed (m/s)'); plt.legend()
    plt.savefig('fatest_speed.png')

    print("Saved: fatest_x.png, fatest_y.png, fatest_traj.png, fatest_speed.png")

if __name__ == "__main__":
    data_directory = '/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data'
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    test_model(data_directory,
               model_path='pretrained_lstm_speed.pt',
               seq_len=10,
               batch_size=32,
               device=device)
