import torch

# ─────────────────────────────────────────────────────────────────────────────
# Modified Candidate <PERSON><PERSON> using μ and σ²
# ─────────────────────────────────────────────────────────────────────────────
# def sample_candidates_gaussian(mu_next, log_var, x_last, K=5, epsilon=1e-6):
#     """
#     Sample K next-position candidates from a Gaussian N(x_last+mu_next, Σ).
    
#     Args:
#       mu_next:  (B,2)  predicted mean offset by the LSTM
#       log_var:  (B,2)  predicted log-variances for each dimension
#       x_last:   (B,2)  last known positions
#       K:        int    number of candidates to sample
#       epsilon:  float  small jitter to keep var>0
    
#     Returns:
#       candidates: Tensor (K,B,2)
#       normals:    Tensor (K,B,2) the raw Gaussian draws (zero-mean)
#     """
#     B = mu_next.size(0)
#     device = mu_next.device

#     # compute per-batch standard deviation
#     var = torch.exp(log_var) + epsilon      # (B,2)
#     std = torch.sqrt(var)                   # (B,2)

#     # Sample K draws of ε ~ N(0,1)
#     eps = torch.randn(K, B, 2, device=device)  # (K,B,2)

#     # scale & shift: x_last + mu_next + ε * σ
#     mu_pos = (x_last + mu_next).unsqueeze(0).expand(K, -1, -1)  # (K,B,2)
#     std_exp = std.unsqueeze(0).expand(K, -1, -1)              # (K,B,2)
#     normals = eps * std_exp                                   # (K,B,2)
#     candidates = mu_pos + normals                             # (K,B,2)

#     return candidates, normals

# def sample_candidates_gaussian(mu_dxdy, mu_speed, log_var_xy, log_var_speed,
#                                 K=5, epsilon=1e-6):
#     """
#     Sample K next-state candidates when the LSTM predicts absolute next-state.

#     Args:
#       mu_dxdy:       Tensor (B,2) predicted absolute next positions (x_next, y_next)
#       mu_speed:      Tensor (B,1) predicted absolute next speeds
#       log_var_xy:    Tensor (B,2) predicted log-variance for x_next, y_next
#       log_var_speed: Tensor (B,1) predicted log-variance for speed
#       K:             int number of candidates to sample
#       epsilon:       float small jitter to keep var>0

#     Returns:
#       cand_pos:   Tensor (K, B, 2) next positions
#       cand_speed: Tensor (K, B, 1) next speeds (non-negative)
#       normals:    Tensor (K, B, 3) the raw Gaussian draws
#     """
#     B = mu_dxdy.size(0)
#     device = mu_dxdy.device

#     # compute variances for each dimension
#     var_xy = torch.exp(log_var_xy) + epsilon       # (B,2)
#     var_speed = torch.exp(log_var_speed) + epsilon # (B,1)
#     var_state = torch.cat([var_xy, var_speed], dim=1)  # (B,3)
#     std_state = torch.sqrt(var_state)                 # (B,3)

#     # sample K draws ε ~ N(0, I)
#     eps = torch.randn(K, B, 3, device=device)         # (K,B,3)
#     normals = eps * std_state.unsqueeze(0)            # (K,B,3)

#     # base = mu_state itself (absolute predictions)
#     mu_state = torch.cat([mu_dxdy, mu_speed], dim=1)  # (B,3)
#     mu_state = mu_state.unsqueeze(0).expand(K, -1, -1) # (K,B,3)

#     # full candidate = mu_state + noise
#     cand_full = mu_state + normals                    # (K,B,3)

#     # split into pos and speed
#     cand_pos = cand_full[..., :2]                     # (K,B,2)
#     cand_speed = cand_full[..., 2:].unsqueeze(-1)     # (K,B,1)

#     # clamp speed to non-negative
#     cand_speed = torch.clamp(cand_speed, min=0.0)

#     return cand_pos, cand_speed, normals



# def sample_candidates_gaussian(mu_xy, mu_speed, log_var_xy, log_var_spd, K):
#     B = mu_xy.size(0)
#     std_xy = torch.exp(log_var_xy / 2)
#     std_spd = torch.exp(log_var_spd / 2)

#     eps_xy = torch.randn(K, B, 2, device=mu_xy.device)
#     eps_spd = torch.randn(K, B, 1, device=mu_speed.device)

#     pred_xy = mu_xy.unsqueeze(0) + eps_xy * std_xy.unsqueeze(0)
#     pred_spd = mu_speed.unsqueeze(0) + eps_spd * std_spd.unsqueeze(0)

#     return pred_xy, pred_spd, eps_xy


# def sample_candidates_gaussian(mu_next, log_var, x_last, K=5):
#     # B = mu_next.size(0)
#     var = torch.exp(log_var) + ε       # (B,2)
#     std = torch.sqrt(var)              # (B,2)
#     eps = torch.randn(K, B, 2)
#     mu_pos = (x_last + mu_next).unsqueeze(0).expand(K, -1, -1)  # (K,B,2)
#     candidates = mu_pos + eps * std.unsqueeze(0)               # (K,B,2)
#     return candidates, eps * std.unsqueeze(0)


# def sample_candidates_gaussian(mu_xy, log_var_xy, x_last, K=5, delta_t=None, ε=1e-6):
#     B = mu_xy.size(0)
#     var   = torch.exp(log_var_xy) + ε
#     std   = torch.sqrt(var)
#     eps   = torch.randn(K, B, 2, device=mu_xy.device)
#     mu_pos = (x_last + mu_xy).unsqueeze(0).expand(K, -1, -1)
#     cand  = mu_pos + eps * std.unsqueeze(0)       # (K,B,2)

#     if delta_t is not None:
#         # compute & return speeds too
#         speeds = torch.norm(cand - mu_pos, dim=2) / delta_t  # (K,B)
#         return cand, speeds, eps * std.unsqueeze(0)

#     return cand, eps * std.unsqueeze(0)


# sampler.py
import torch

import torch

def sample_candidates_gaussian(mu_dxdy, mu_speed, log_var, x_last, delta_t=None, eps=1e-6):
    """
    Args:
      mu_dxdy:   (K, B, 2)  — LSTM’s predicted Δx,Δy for each of the K sequences in the batch
      mu_speed:  (K, B, 1)  — LSTM’s predicted Δspeed
      log_var:   (K, B, 3)  — [logσ²_x, logσ²_y, logσ²_speed]
      x_last:    (K, B, 2)  — the last (x,y) of each of the K histories (you already have hist_k)
      delta_t:   float     — if provided, compute returned “velocities”
      eps:       float     — jitter to keep variances >0

    Returns:
      cand_xy:   (K, B, 2)
      cand_spd:  (K, B, 1)
      vel?      (K, B)     if delta_t is not None
    """
    # split out position vs speed variances
    log_var_xy = log_var[..., :2]    # (K,B,2)
    log_var_sp = log_var[..., 2:]    # (K,B,1)

    # std = √(exp(log_var) + eps)
    std_xy = torch.sqrt(torch.exp(log_var_xy) + eps)
    std_sp = torch.sqrt(torch.exp(log_var_sp) + eps)

    # sample noise
    eps_xy = torch.randn_like(std_xy)
    eps_sp = torch.randn_like(std_sp)

    # sample candidates
    cand_xy  = x_last + mu_dxdy + eps_xy * std_xy    # (K,B,2)
    cand_spd = mu_speed +         eps_sp * std_sp    # (K,B,1)

    if delta_t is not None:
        # if you want to compute speeds from positional change:
        vel = torch.norm(cand_xy - x_last, dim=2) / delta_t  # (K,B)
        return cand_xy, cand_spd, vel

    return cand_xy, cand_spd



# ─────────────────────────────────────────────────────────────────────────────
# Integration into predict_one_step
# ─────────────────────────────────────────────────────────────────────────────
def predict_one_step_with_uncertainty(
    lstm_model,
    x_hist,
    risk_model,
    av_features,
    gaze_time,
    K=5
):
    """
    One‐step prediction that samples based on the LSTM's Gaussian output.
    
    Arguments:
      lstm_model : LSTMGaussianPredictor
      x_hist     : Tensor (B,T,2)
      risk_model : your RiskModel(nn.Module)
      av_features: dict of B‐shaped AV feature Tensors
      gaze_time  : Tensor (B,)
      K          : number of Gaussian candidates
    
    Returns:
      chosen_next: Tensor (B,2) lowest‐risk next positions
      mu_next    : Tensor (B,2) LSTM mean offsets
      cand       : Tensor (K,B,2) all sampled candidates
      rpc        : Tensor (K,B) risk+cost scores
    """
    # 1) get Gaussian output from LSTM
    mu_next, log_var = lstm_model(x_hist)   # both (B,2)
    x_last = x_hist[:, -1, :]               # (B,2)

    # 2) sample K candidates from N(x_last+μ, diag(σ²))
    cand, normals = sample_candidates_gaussian(mu_next, log_var, x_last, K=K)

    # 3) compute action params (e.g. velocity magnitude & heading)
    Δt = 0.1
    vel = torch.norm(cand - x_last.unsqueeze(0), dim=2) / Δt  # (K,B)
    dtheta = torch.zeros_like(vel)                           # (K,B)
    action_params = torch.stack([vel, dtheta], dim=2)        # (K,B,2)

    # 4) stack AV features to (K,B,...)
    candidate_states = {}
    for key, val in av_features.items():
        candidate_states[key] = val.unsqueeze(0).expand(K, -1, *val.shape[1:])

    # ped_pos:
    candidate_states['ped_pos'] = cand  # (K,B,2)

    # 5) risk + cost
    gaze_KB = gaze_time.unsqueeze(0).expand(K, -1)  # (K,B)
    rpc = risk_model(candidate_states, action_params, gaze_KB)  # (K,B)

    # 6) pick lowest‐risk candidate
    idx_min = torch.argmin(rpc, dim=0)  # (B,)
    chosen = cand[idx_min, torch.arange(x_hist.size(0))]  # (B,2)

    return chosen, mu_next, cand, rpc
