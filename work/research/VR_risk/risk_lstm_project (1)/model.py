
import torch
import torch.nn as nn
import torch.nn.functional as F

class GaussianTrajectoryModel(nn.Module):
    def __init__(self, input_size=3, hidden_size=64, num_layers=2):
        super(GaussianTrajectoryModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.rnn = nn.GRU(input_size, hidden_size, num_layers, batch_first=True)
        self.fc_mu = nn.Linear(hidden_size, 2)
        self.fc_sigma = nn.Linear(hidden_size, 2)
        self.fc_rho = nn.Linear(hidden_size, 1)

    def forward(self, x):
        batch_size = x.size(0)
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        out, _ = self.rnn(x, h0)
        h = out[:, -1, :]
        mu = self.fc_mu(h)
        log_sigma = self.fc_sigma(h)
        sigma = torch.exp(log_sigma)
        rho = torch.tanh(self.fc_rho(h))
        return mu, sigma, rho
