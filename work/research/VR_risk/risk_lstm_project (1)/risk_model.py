


# import pandas as pd
# import numpy as np
# from scipy.interpolate import interp1d
# from scipy.optimize import minimize_scalar
# from matplotlib.path import Path as MplPath
# from scipy.interpolate import interp1d
# from scipy.spatial import cKDTree
# import torch
# import torch.nn as nn


# # ─────────────────────────────────────────────────────────────────────────────
# #  Helper functions (unchanged)
# # ─────────────────────────────────────────────────────────────────────────────

# def load_intended_path(csv_path):
#     df = pd.read_csv(csv_path)
#     return df['x'].values.astype(np.float32), df['y'].values.astype(np.float32)


# def load_feasible_region(npy_path):
#     return np.load(npy_path).astype(np.float32)



# # def interpolate_path(x, y):
# #     dx, dy = np.diff(x), np.diff(y)
# #     dist = np.sqrt(dx**2 + dy**2)
# #     cum_dist = np.insert(np.cumsum(dist), 0, 0)
# #     return interp1d(cum_dist, x, bounds_error=False, fill_value=(x[0], x[-1])), \
# #            interp1d(cum_dist, y, bounds_error=False, fill_value=(y[0], y[-1])), \
# #            cum_dist[-1]


# # def distance_to_path(px, py, x_interp, y_interp, max_d):
# #     def f(s): return float(np.hypot(px - x_interp(s), py - y_interp(s)))
# #     res = minimize_scalar(f, bounds=(0., max_d), method='bounded')
# #     return float(res.fun)


# def torch_distance_to_path_batch(ped_xy, x_interp, y_interp, max_dist):
#     # ped_xy: (K, B, 2)
#     import torch
#     K, B, _ = ped_xy.shape
#     dists = []
#     for k in range(K):
#         row = []
#         for i in range(B):
#             px, py = ped_xy[k, i, 0].item(), ped_xy[k, i, 1].item()
#             def f(s): return np.hypot(px - x_interp(s), py - y_interp(s))
#             res = minimize_scalar(f, bounds=(0., max_dist), method='bounded')
#             row.append(res.fun)
#         dists.append(row)
#     return torch.tensor(dists, dtype=torch.float32, device=ped_xy.device)

# def point_to_segment_distance(p, a, b):
#     p, a, b = map(np.asarray, (p,a,b))
#     ap, ab = p-a, b-a; ab2 = ab.dot(ab)
#     if ab2 == 0: return float(np.linalg.norm(ap))
#     t = np.clip(ap.dot(ab)/ab2, 0, 1)
#     return float(np.linalg.norm(p - (a + t*ab)))


# def interpolate_path(x: np.ndarray, y: np.ndarray):
#     dx = np.diff(x)
#     dy = np.diff(y)
#     distances = np.sqrt(dx**2 + dy**2)
#     cd = np.insert(np.cumsum(distances), 0, 0.).astype(np.float32)
#     x_interp = interp1d(cd, x, kind='linear', bounds_error=False, fill_value=(x[0], x[-1]))
#     y_interp = interp1d(cd, y, kind='linear', bounds_error=False, fill_value=(y[0], y[-1]))
#     return x_interp, y_interp, float(cd[-1])

# class RiskModel(nn.Module):
#     def __init__(self, intended_path_csv, feasible_region_npy,
#                  A, alpha, B, C, beta, gamma, D, k, E):
#         super().__init__()
#         px, py = load_intended_path(intended_path_csv)
#         self.x_interp, self.y_interp, self.max_path = interpolate_path(px, py)
#         self.path_points = np.stack([px, py], axis=1)
#         self.kdtree_path = cKDTree(self.path_points)

#         poly = load_feasible_region(feasible_region_npy)
#         self._poly = poly
#         self.kdtree_poly = cKDTree(poly)
#         self.feasible_path = MplPath(poly)

#         self.A = nn.Parameter(torch.tensor(A))
#         self.alpha = nn.Parameter(torch.tensor(alpha))
#         self.B = nn.Parameter(torch.tensor(B))
#         self.C = nn.Parameter(torch.tensor(C))
#         self.beta = nn.Parameter(torch.tensor(beta))
#         self.gamma = nn.Parameter(torch.tensor(gamma))
#         self.D = nn.Parameter(torch.tensor(D))
#         self.k = nn.Parameter(torch.tensor(k))
#         self.E = nn.Parameter(torch.tensor(E))
#         self.max_dev = 10.0

#         self.q1 = nn.Parameter(torch.tensor(10.0))
#         self.q2 = nn.Parameter(torch.tensor(10.0))

#     def forward(self, candidate_states, action_params, gaze):
#         ped = candidate_states['ped_pos']  # (K, B, 2)
#         AV  = candidate_states['AV_pos']
#         vAV = candidate_states['v_AV']
#         aAV = candidate_states['a_AV']
#         btype = candidate_states['b_type']
#         bintn = candidate_states['b_intensity']
#         sstop = candidate_states['s_stop']
#         TAV = candidate_states['TAV']

#         d = torch.norm(ped - AV, dim=2)
#         dfac = torch.exp(-self.alpha * (d - 0.1) / (10.0 - 0.1))
#         sfac = 1.0 + (vAV / 8.33)
#         R1 = (self.A * dfac * sfac / (self.A * 2)).clamp(0, 1)

#         # R2: path deviation risk (fast with KD-tree)
#         K, B, _ = ped.shape
#         ped_np = ped.view(-1, 2).cpu().numpy()
#         _, dists = self.kdtree_path.query(ped_np, k=1)
#         d_path = torch.tensor(dists.reshape(K, B), device=ped.device)
#         R2 = (self.B * d_path / (self.B * self.max_dev)).clamp(0, 1)

#         # R3: boundary risk (fast with KD-tree)
#         inside_mask = np.array([self.feasible_path.contains_point(p) for p in ped_np]).reshape(K, B)
#         boundary_dists = np.zeros((K, B), dtype=np.float32)
#         outside_idx = np.where(~inside_mask)
#         if len(outside_idx[0]) > 0:
#             query_pts = ped_np[~inside_mask.reshape(-1)]
#             _, dists_poly = self.kdtree_poly.query(query_pts, k=1)
#             boundary_dists[outside_idx] = dists_poly
#         raw_R3 = self.E * (torch.tensor(boundary_dists, device=ped.device) ** 2)
#         R3 = (raw_R3 / (50.0 * self.E)).clamp(0, 1)

#         # R4: behavioral risk with Gaussian-sampled v_p
#         TTC = (d / vAV.clamp(min=0.1)).clamp(max=100.0)
#         sigma2 = (self.k / TAV.clamp(min=1e-2)).clamp(min=1e-4)
#         epsilon = torch.randn_like(vAV) * sigma2.sqrt()
#         vp = vAV + epsilon
#         delta_v = (vAV - vp).abs()
#         fa = torch.exp(-self.beta * aAV.abs())
#         s = sstop.clamp(min=1.0)
#         R4 = (self.C * (1.0 / TTC + (delta_v / s) * fa)).clamp(0, 1)

#         # R5: perceptual uncertainty
#         R5 = (self.D * torch.exp(-(delta_v ** 2) / (2 * sigma2)) / self.D).clamp(0, 1)

#         v_p, dth = action_params[..., 0], action_params[..., 1]
#         Cost = self.q1 * (v_p ** 2) + self.q2 * (dth ** 2)


#         # Normalize total risk per sample safely
#         Rtot = R1 + R2 + R3 + R4 + R5 #+ Cost
#         std = Rtot.std(dim=0, keepdim=True).clamp(min=1e-4)
#         Rtot = (Rtot - Rtot.mean(dim=0, keepdim=True)) / std

#         return torch.nan_to_num(Rtot)


       

import numpy as np
import pandas as pd
from scipy.interpolate import interp1d
import torch
import torch.nn as nn


def interpolate_path(x: np.ndarray, y: np.ndarray):
    """
    Create linear interpolators for a polyline defined by x, y coordinates.

    Returns:
        x_interp: callable mapping distance along path to x-coordinate
        y_interp: callable mapping distance along path to y-coordinate
        max_dist: total length of the path
    """
    dx = np.diff(x)
    dy = np.diff(y)
    distances = np.sqrt(dx**2 + dy**2)
    cd = np.insert(np.cumsum(distances), 0, 0.).astype(np.float32)
    x_interp = interp1d(cd, x, kind='linear', bounds_error=False, fill_value=(x[0], x[-1]))
    y_interp = interp1d(cd, y, kind='linear', bounds_error=False, fill_value=(y[0], y[-1]))
    return x_interp, y_interp, float(cd[-1])


def load_intended_path(csv_path: str):
    """
    Load intended path from a CSV file with columns 'x' and 'y'.

    Returns:
        x: numpy array of x-coordinates (float32)
        y: numpy array of y-coordinates (float32)
    """
    df = pd.read_csv(csv_path)
    return df['x'].values.astype(np.float32), df['y'].values.astype(np.float32)


def load_feasible_region(npy_path: str):
    """
    Load a feasible-region polygon from a .npy file containing Nx2 float32 points.

    Returns:
        poly: numpy array of polygon vertices (float32)
    """
    return np.load(npy_path).astype(np.float32)


class RiskModel(nn.Module):
    def __init__(self,
                 intended_path_csv: str,
                 feasible_region_npy: str,
                 A, alpha, B, C, beta, gamma, D, k, E):
        super().__init__()

        # 1) Load & tensorize the "intended" polyline
        px, py = load_intended_path(intended_path_csv)
        pts = torch.tensor(list(zip(px, py)), dtype=torch.float32)  # [P,2]
        self.register_buffer('path_pts', pts)
        # Precompute segments
        self.register_buffer('seg_start', pts[:-1])                 # [P-1,2]
        self.register_buffer('seg_vec', pts[1:] - pts[:-1])         # [P-1,2]
        self.register_buffer('seg_len2', (self.seg_vec**2).sum(dim=1, keepdim=True))  # [P-1,1]
        self.max_dev = 10.0

        # 2) Load & tensorize the feasible-region polygon
        poly = load_feasible_region(feasible_region_npy)            # numpy[N,2]
        poly_pts = torch.tensor(poly, dtype=torch.float32)          # [Q,2]
        self.register_buffer('poly_pts', poly_pts)
        self.register_buffer('poly_start', poly_pts[:-1])           # [Q-1,2]
        self.register_buffer('poly_vec', poly_pts[1:] - poly_pts[:-1])  # [Q-1,2]
        self.register_buffer('poly_len2', (self.poly_vec**2).sum(dim=1, keepdim=True))  # [Q-1,1]

        # 3) Learnable parameters
        for name, val in [('A',A),('alpha',alpha),('B',B),('C',C),
                          ('beta',beta),('gamma',gamma),
                          ('D',D),('k',k),('E',E)]:
            setattr(self, name, nn.Parameter(torch.tensor(val, dtype=torch.float32)))
        self.q1 = nn.Parameter(torch.tensor(1.0))
        self.q2 = nn.Parameter(torch.tensor(1.0))

    def _point_to_segs(self, pts_flat, seg_start, seg_vec, seg_len2):
        """
        Compute minimum distance from each point to a set of line segments.

        Args:
            pts_flat: Tensor[N,2] of points
            seg_start: Tensor[S,2] segment start points
            seg_vec:   Tensor[S,2] segment direction vectors
            seg_len2:  Tensor[S,1] squared segment lengths

        Returns:
            Tensor[N] of minimum distances
        """
        # [N,S,2]
        v = pts_flat.unsqueeze(1) - seg_start.unsqueeze(0)
        proj = (v * seg_vec.unsqueeze(0)).sum(-1) / seg_len2.squeeze(1)
        proj_clamped = proj.clamp(0.0, 1.0).unsqueeze(-1)
        closest = seg_start.unsqueeze(0) + proj_clamped * seg_vec.unsqueeze(0)
        d2 = ((pts_flat.unsqueeze(1) - closest)**2).sum(-1)
        return d2.min(dim=1).values.sqrt()

    def forward(self, candidate_states, action_params, gaze=None):
        ped = candidate_states['ped_pos']  # [K,B,2]
        AV  = candidate_states['AV_pos']
        vAV = candidate_states['v_AV']
        aAV = candidate_states['a_AV']
        sstop = candidate_states['s_stop']
        TAV = candidate_states['TAV']

        # R1: proximity risk
        d = torch.norm(ped - AV, dim=2)
        dfac = torch.exp(-self.alpha * (d - 0.1) / (10.0 - 0.1))
        sfac = 1.0 + (vAV / 8.33)
        R1 = (self.A * dfac * sfac / (2*self.A)).clamp(0,1)

        K, B, _ = ped.shape
        pts_flat = ped.view(-1,2)  # [N,2]

        # R2: path deviation
        d_path = self._point_to_segs(pts_flat,
                                     self.seg_start, self.seg_vec, self.seg_len2)
        R2 = (self.B * d_path.view(K,B) / (self.B * self.max_dev)).clamp(0,1)

        # R3: boundary distance risk
        d_bound = self._point_to_segs(pts_flat,
                                      self.poly_start, self.poly_vec, self.poly_len2)
        R3 = (self.E * (d_bound**2).view(K,B) / (50.0 * self.E)).clamp(0,1)

        # R4: behavioral risk
        TTC = (d / vAV.clamp(min=0.1)).clamp(max=100.0)
        sigma2 = (self.k / TAV.clamp(min=1e-2)).clamp(min=1e-4)
        eps = torch.randn_like(vAV)
        vp = vAV + eps * sigma2.sqrt()
        delta_v = (vAV - vp).abs()
        fa = torch.exp(-self.beta * aAV.abs())
        s = sstop.clamp(min=1.0)
        R4 = (self.C * (1.0 / TTC + (delta_v / s) * fa)).clamp(0,1)

        # R5: perceptual uncertainty
        R5 = (self.D * torch.exp(-(delta_v**2)/(2*sigma2)) / self.D).clamp(0,1)

        # Optional action-cost
        v_p, dth = action_params[...,0], action_params[...,1]
        Cost = self.q1 * (v_p**2) + self.q2 * (dth**2)

        # aggregate & normalize
        Rtot = R1 + R2 + R3 + R4 + R5  + Cost
        mean = Rtot.mean(dim=0, keepdim=True)
        std  = Rtot.std(dim=0, keepdim=True).clamp(min=1e-4)
        Rnorm = (Rtot - mean) / std

        return torch.nan_to_num(Rnorm)
