import matplotlib.pyplot as plt
import torch
import os
import numpy as np

def visualize_trajectory_rollout(obs, pred_gt, pred_seq, candidates=None, av_pos=None,
                                  feasible_poly=None, intended_path=None, save_path=None, title="Trajectory Prediction"):
    plt.figure(figsize=(6, 6))
    plt.plot(obs[:, 0], obs[:, 1], 'bo-', label='Observed')
    plt.plot(pred_gt[:, 0], pred_gt[:, 1], 'g^-', label='Ground Truth')
    plt.plot(pred_seq[:, 0], pred_seq[:, 1], 'rs-', label='Prediction')

    if candidates is not None:
        for i, cand in enumerate(candidates):
            plt.plot(cand[:, 0], cand[:, 1], '--', alpha=0.4, label=f'Candidate {i+1}' if i < 5 else None)

    # if av_pos is not None:
    #     plt.plot(av_pos[0], av_pos[1], 'kx', markersize=10, label='AV Pos')

    # if feasible_poly is not None:
    #     poly_np = np.array(feasible_poly)
    #     plt.plot(poly_np[:, 0], poly_np[:, 1], 'c--', label='Feasible Region')

    # if intended_path is not None:
    #     path_np = np.array(intended_path)
    #     plt.plot(path_np[:, 0], path_np[:, 1], 'm-', label='Intended Path')

    plt.title(title)
    plt.xlabel('X Position')
    plt.ylabel('Y Position')
    plt.legend()
    plt.grid(True)
    plt.axis('equal')
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()


def visualize_batch_rollout(batch_obs, batch_gt, batch_pred, batch_cands=None, output_dir="viz_rollouts",
                             av_positions=None, feasible_poly=None, intended_path=None):
    B = batch_obs.shape[0]
    os.makedirs(output_dir, exist_ok=True)
    for i in range(B):
        obs = batch_obs[i]
        gt  = batch_gt[i]
        pred = batch_pred[i]
        cands_np = None
        if batch_cands is not None:
            cands_np = [batch_cands[i, k].cpu().numpy() for k in range(batch_cands.shape[1])]

        av_pos = av_positions[i] if av_positions is not None else None

        save_path = os.path.join(output_dir, f"rollout_{i:03d}.png")
        visualize_trajectory_rollout(obs, gt, pred, cands_np, av_pos,
                                     feasible_poly, intended_path, save_path)


def prepare_candidate_batch(candidate_trajectories):
    if len(candidate_trajectories) == 0:
        return None
    T = len(candidate_trajectories)
    K, B, _ = candidate_trajectories[0].shape
    out = torch.stack(candidate_trajectories, dim=0)  # (T, K, B, 2)
    out = out.permute(2, 1, 0, 3)  # (B, K, T, 2)
    return out


# def compute_trajectory_smoothness(pred_seq, last_obs):
#     """
#     Encourage the predicted trajectory to be smooth.
#     pred_seq: (B, T, 2)
#     last_obs: (B, 2)
#     """
#     diff = pred_seq[:, 0, :] - last_obs  # first step
#     penalty = torch.norm(diff, dim=1).mean()
#     for t in range(1, pred_seq.shape[1]):
#         step_diff = pred_seq[:, t, :] - pred_seq[:, t - 1, :]
#         penalty += torch.norm(step_diff, dim=1).mean()
#     return penalty / pred_seq.shape[1]
# import torch

import torch

def compute_trajectory_smoothness(pred_seq: torch.Tensor, last_obs: torch.Tensor) -> torch.Tensor:
    """
    Encourage the predicted trajectory to be smooth.

    Supports two input shapes:
      - pred_seq: (N, T, 2) and last_obs: (N, 2)
      - pred_seq: (K, B, T, 2) and last_obs: (K, B, 2)

    Returns a scalar penalty.
    """
    # if we have K×B trajectories, flatten them into a single batch of size N = K*B
    if pred_seq.dim() == 4:
        K, B, T, D = pred_seq.shape
        pred_seq = pred_seq.view(K * B, T, D)       # → (K*B, T, 2)
        last_obs = last_obs.view(K * B, D)          # → (K*B, 2)

    # now pred_seq: (N, T, 2), last_obs: (N, 2)
    N, T, _ = pred_seq.shape

    # first‐step difference
    diff0 = pred_seq[:, 0, :] - last_obs         # (N, 2)
    penalty = torch.norm(diff0, dim=1).mean()

    # inter‐step smoothness
    for t in range(1, T):
        step_diff = pred_seq[:, t, :] - pred_seq[:, t - 1, :]  # (N, 2)
        penalty += torch.norm(step_diff, dim=1).mean()

    # normalize by number of steps
    return penalty / T


# def compute_trajectory_smoothness(pred_seq, last_obs, weight: float = 1.0):
#     """
#     Smoothness penalty on a predicted trajectory (or K candidate trajectories).
    
#     Args:
#       pred_seq: Tensor, either
#         - (B, T, 2) for a single set of B trajectories, or
#         - (K, B, T, 2) for K candidate trajectories per batch.
#       last_obs: Tensor, same leading dims as pred_seq without the T axis:
#         - (B, 2) if pred_seq is (B, T, 2), or
#         - (K, B, 2) if pred_seq is (K, B, T, 2).
#       weight:  scalar weight on the returned penalty.
    
#     Returns:
#       A scalar Tensor = weight × (mean per‐step L2 norm).
#     """
#     dims = pred_seq.dim()
#     if dims == 3:
#         # Single‐batch case: pred_seq (B,T,2), last_obs (B,2)
#         B, T, _ = pred_seq.shape
#         # first‐step difference
#         diff0 = pred_seq[:, 0, :] - last_obs              # (B,2)
#         penalty = torch.norm(diff0, dim=-1).mean()       # scalar
#         # subsequent steps
#         for t in range(1, T):
#             step_diff = pred_seq[:, t, :] - pred_seq[:, t-1, :]
#             penalty += torch.norm(step_diff, dim=-1).mean()
#         penalty = penalty / T

#     elif dims == 4:
#         # Candidate‐batch case: pred_seq (K,B,T,2), last_obs (K,B,2)
#         K, B, T, _ = pred_seq.shape
#         # first‐step
#         diff0 = pred_seq[:, :, 0, :] - last_obs            # (K,B,2)
#         penalty = torch.norm(diff0, dim=-1).mean()        # average over K,B
#         # subsequent
#         for t in range(1, T):
#             step_diff = pred_seq[:, :, t, :] - pred_seq[:, :, t-1, :]
#             penalty += torch.norm(step_diff, dim=-1).mean()
#         penalty = penalty / T

#     else:
#         raise ValueError(f"compute_trajectory_smoothness: expected 3D or 4D pred_seq, got {dims}D.")

#     return weight * penalty
