
import torch
import numpy as np

def sample_trajectories(mu, sigma, rho, num_samples=20):
    N = mu.shape[0]
    samples = []
    for _ in range(num_samples):
        eps = torch.randn_like(mu)
        cov = torch.zeros(N, 2, 2).to(mu.device)
        cov[:, 0, 0] = sigma[:, 0] ** 2
        cov[:, 1, 1] = sigma[:, 1] ** 2
        cov[:, 0, 1] = cov[:, 1, 0] = rho.squeeze(1) * sigma[:, 0] * sigma[:, 1]
        L = torch.linalg.cholesky(cov)
        sample = mu.unsqueeze(-1) + torch.bmm(L, eps.unsqueeze(-1))
        samples.append(sample.squeeze(-1))
    return torch.stack(samples, dim=1)

def select_min_risk_trajectory(samples, risk_model, initial_state, df, path_line, feasible_region, step):
    risks = []
    for sample in samples:
        state = risk_model.next_state(initial_state, sample.cpu().numpy(), df, step)
        risk = risk_model.total_risk(state, path_line, feasible_region, risk_model.params[:9])
        risks.append(risk)
    risks = np.array(risks)
    best_idx = np.argmin(risks)
    return samples[best_idx], risks
