
# risk_lstm_colab_demo.py

# Install requirements if needed (Uncomment when running outside Colab)
# !pip install torch numpy matplotlib scipy

import torch
import numpy as np
from risk_lstm_project.model import GaussianTrajectoryModel
from risk_lstm_project.losses import bivariate_gaussian_nll
from risk_lstm_project.sample_and_score import sample_trajectories, select_min_risk_trajectory

# Define dummy training data
batch_size, seq_len, input_size = 32, 10, 3
x = torch.randn(batch_size, seq_len, input_size)
y = torch.randn(batch_size, 2)

# Initialize model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = GaussianTrajectoryModel().to(device)
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# Train for a few epochs
for epoch in range(10):
    model.train()
    x_batch, y_batch = x.to(device), y.to(device)
    optimizer.zero_grad()
    mu, sigma, rho = model(x_batch)
    loss = bivariate_gaussian_nll(mu, sigma, rho, y_batch)
    loss.backward()
    optimizer.step()
    print(f"Epoch {epoch+1}: Loss = {loss.item():.4f}")

# Example of sampling (mock, replace with real state/risk logic later)
samples = sample_trajectories(mu, sigma, rho, num_samples=5)
print("Sampled trajectories shape:", samples.shape)

# Here you'd use select_min_risk_trajectory() with your actual risk_model
