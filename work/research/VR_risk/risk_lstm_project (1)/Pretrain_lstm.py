

# # train_pretrain_lstm.py

# import os
# import math
# import argparse
# import torch
# import torch.nn as nn
# import torch.optim as optim
# import numpy as np
# from torch.utils.data import DataLoader
# from sklearn.metrics import mean_squared_error
# import matplotlib.pyplot as plt

# from vr_dataset import VRPedestrianDataset
# from pred_lstm import LSTMGaussianPredictor, gaussian_nll

# def compute_ade_fde(gt, pred):
#     diffs = gt - pred
#     dists = np.linalg.norm(diffs, axis=2)
#     ADE = dists.mean()
#     FDE = dists[:, -1].mean()
#     return ADE, FDE

# def pretrain_lstm(args):
#     device = 'cuda' if torch.cuda.is_available() else 'cpu'

#     dataset = VRPedestrianDataset(
#         data_path=args.data_dir,
#         obs_len=args.obs_len,
#         pred_len=args.pred_len
#     )
#     print("Dataloaded")

#     n = int(len(dataset)*args.train_frac)
#     train_ds, val_ds = torch.utils.data.random_split(dataset, [n, len(dataset)-n])
#     train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
#     val_loader = DataLoader(val_ds, batch_size=args.batch_size, shuffle=False)

#     lstm = LSTMGaussianPredictor(in_size=3, hidden_size=args.hidden, num_layers=args.layers).to(device)
#     optimizer = optim.Adam(lstm.parameters(), lr=args.lr)
#     best_rmse = float('inf')

#     train_losses, val_rmses, val_ades, val_fdes = [], [], [], []

#     for epoch in range(1, args.epochs + 1):
#         lstm.train()
#         total_loss = 0.0
#         for obs, pred_gt, meta in train_loader:
#             obs, pred_gt = obs.to(device), pred_gt.to(device)
#             hist = obs[..., :3]  # input is (B, seq_len, 3)
#             target = pred_gt[:, 0, :2]  # predict only next (x, y)

#             optimizer.zero_grad()
#             mu, log_var = lstm(hist)
#             loss = gaussian_nll(mu, log_var, target)

#             if torch.isnan(loss):
#                 print("NaN in LSTM loss!")
#                 continue

#             loss.backward()
#             optimizer.step()
#             total_loss += loss.item()

#         avg_train_loss = total_loss / len(train_loader)
#         train_losses.append(avg_train_loss)

#         # Validation
#         lstm.eval()
#         all_preds, all_gts = [], []
#         with torch.no_grad():
#             for obs, pred_gt, meta in val_loader:
#                 obs = obs.to(device)
#                 hist = obs[..., :3]
#                 mu, _ = lstm(hist)
#                 all_preds.append(mu.cpu().numpy())
#                 all_gts.append(pred_gt[:, 0, :2].cpu().numpy())

#         all_preds = np.concatenate(all_preds)
#         all_gts = np.concatenate(all_gts)
#         val_rmse = math.sqrt(mean_squared_error(all_gts, all_preds))
#         val_rmses.append(val_rmse)

#         # ADE/FDE with dummy 1-step replication
#         ade, fde = compute_ade_fde(all_gts[:, None, :], all_preds[:, None, :])
#         val_ades.append(ade)
#         val_fdes.append(fde)

#         if val_rmse < best_rmse:
#             best_rmse = val_rmse
#             torch.save(lstm.state_dict(), args.checkpoint)

#         print(f"Epoch {epoch:03d} | Train NLL: {avg_train_loss:.4f} | Val RMSE: {val_rmse:.4f}")

#     print("LSTM pretraining complete. Best RMSE:", best_rmse)

#     # Plot results
#     epochs = range(1, args.epochs + 1)
#     plt.figure()
#     plt.plot(epochs, train_losses, label='Train NLL')
#     plt.plot(epochs, val_rmses, label='Val RMSE')
#     plt.plot(epochs, val_ades, label='Val ADE')
#     plt.plot(epochs, val_fdes, label='Val FDE')
#     plt.xlabel("Epoch")
#     plt.ylabel("Metric")
#     plt.title("LSTM Pretraining Progress")
#     plt.legend()
#     plt.grid(True)
#     plt.savefig("pretrain_lstm_metrics.png")

# if __name__ == "__main__":
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--data_dir', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
#     parser.add_argument('--obs_len', type=int, default=10)
#     parser.add_argument('--pred_len', type=int, default=10)
#     parser.add_argument('--train_frac', type=float, default=0.8)
#     parser.add_argument('--batch_size', type=int, default=16)
#     parser.add_argument('--hidden', type=int, default=64)
#     parser.add_argument('--layers', type=int, default=2)
#     parser.add_argument('--lr', type=float, default=1e-4)
#     parser.add_argument('--epochs', type=int, default=30)
#     parser.add_argument('--checkpoint', type=str, default='pretrained_lstm.pt')
#     args = parser.parse_args()

#     pretrain_lstm(args)
import os
import math
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader
from sklearn.metrics import mean_squared_error
import matplotlib.pyplot as plt

from vr_dataset import VRPedestrianDataset
from pred_lstm import LSTMGaussianPredictor, gaussian_nll


def compute_ade_fde(gt, pred):
    """
    gt, pred: np.array (B, T_pred, 2)
    returns: ADE, FDE for positions only
    """
    diffs = gt - pred
    dists = np.linalg.norm(diffs, axis=2)
    ADE = dists.mean()
    FDE = dists[:, -1].mean()
    return ADE, FDE


def pretrain_lstm(args):
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # load dataset: obs consists of [x, y, speed] + other dims, but we use first 3
    dataset = VRPedestrianDataset(
        data_path=args.data_dir,
        obs_len=args.obs_len,
        pred_len=args.pred_len
    )
    print("Dataloaded, total samples=", len(dataset))

    # train/val split
    n = int(len(dataset) * args.train_frac)
    train_ds, val_ds = torch.utils.data.random_split(dataset, [n, len(dataset) - n])
    train_loader = DataLoader(train_ds, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_ds, batch_size=args.batch_size, shuffle=False)

    # model: input size=3 dims ([x,y,speed])
    lstm = LSTMGaussianPredictor(in_size=3,
                                 hidden_size=args.hidden,
                                 num_layers=args.layers).to(device)
    optimizer = optim.Adam(lstm.parameters(), lr=args.lr)
    best_rmse = float('inf')

    # metrics storage
    train_losses = []
    val_pos_rmse = []
    val_pos_ade  = []
    val_pos_fde  = []
    val_speed_rmse = []

    for epoch in range(1, args.epochs + 1):
        # --- training ---
        lstm.train()
        total_loss = 0.0
        for obs, pred_gt, meta in train_loader:
            obs = obs.to(device)
            # input: last obs_len states, take [:,:, :3]
            hist = obs[..., :3]
            # target: next step [x, y, speed]
            target = pred_gt[:, 0, :3].to(device)

            optimizer.zero_grad()
            mu_dxdy, mu_speed, log_var = lstm(hist)
            # combine mu (B,3)
            mu = torch.cat([mu_dxdy, mu_speed], dim=1)
            loss = gaussian_nll(mu, log_var, target)
            if torch.isnan(loss):
                print("NaN in LSTM loss at epoch", epoch)
                continue
            loss.backward()
            optimizer.step()
            total_loss += loss.item()

        avg_train_loss = total_loss / len(train_loader)
        train_losses.append(avg_train_loss)

        # --- validation ---
        lstm.eval()
        all_pos_preds, all_pos_gts = [], []
        all_speed_preds, all_speed_gts = [], []
        # for ADE/FDE we need shape (B, T_pred=1, 2)
        for obs, pred_gt, meta in val_loader:
            obs = obs.to(device)
            hist = obs[..., :3]
            with torch.no_grad():
                mu_dxdy, mu_speed, _ = lstm(hist)
            mu = torch.cat([mu_dxdy, mu_speed], dim=1).cpu().numpy()
            # absolute next [x,y,s]
            all_pos_preds.append(mu[:, :2])
            all_speed_preds.append(mu[:, 2])
            # ground-truth next
            gt_np = pred_gt[:, 0, :3].cpu().numpy()
            all_pos_gts.append(gt_np[:, :2])
            all_speed_gts.append(gt_np[:, 2])

        pos_pred = np.vstack(all_pos_preds)
        pos_gt   = np.vstack(all_pos_gts)
        speed_pred = np.concatenate(all_speed_preds)
        speed_gt   = np.concatenate(all_speed_gts)

        # position metrics
        rmse_pos = math.sqrt(mean_squared_error(pos_gt, pos_pred))
        ade, fde = compute_ade_fde(pos_gt[:, None, :], pos_pred[:, None, :])
        val_pos_rmse.append(rmse_pos)
        val_pos_ade.append(ade)
        val_pos_fde.append(fde)
        # speed metric
        rmse_speed = math.sqrt(mean_squared_error(speed_gt, speed_pred))
        val_speed_rmse.append(rmse_speed)

        # checkpoint on position rmse
        if rmse_pos < best_rmse:
            best_rmse = rmse_pos
            torch.save(lstm.state_dict(), args.checkpoint)

        # log
        print(f"Epoch {epoch:03d} | Train NLL: {avg_train_loss:.4f}"
              f" | Pos RMSE: {rmse_pos:.4f}, ADE: {ade:.4f}, FDE: {fde:.4f}"
              f" | Speed RMSE: {rmse_speed:.4f}")

    print("Pretraining complete. Best Pos RMSE:", best_rmse)

    # --- Plot metrics ---
    epochs = np.arange(1, args.epochs+1)
    plt.figure()
    plt.plot(epochs, train_losses, label='Train NLL')
    plt.plot(epochs, val_pos_rmse, label='Val Pos RMSE')
    plt.plot(epochs, val_pos_ade,  label='Val ADE')
    plt.plot(epochs, val_pos_fde,  label='Val FDE')
    plt.plot(epochs, val_speed_rmse, label='Val Speed RMSE')
    plt.xlabel('Epoch')
    plt.ylabel('Metric')
    plt.title('LSTM Pretraining Progress')
    plt.legend()
    plt.grid(True)
    plt.savefig('pretrain_lstm_speed_metrics.png')

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_dir', type=str,
                        default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/')
    parser.add_argument('--obs_len', type=int, default=10)
    parser.add_argument('--pred_len', type=int, default=10)
    parser.add_argument('--train_frac', type=float, default=0.8)
    parser.add_argument('--batch_size', type=int, default=16)
    parser.add_argument('--hidden', type=int, default=64)
    parser.add_argument('--layers', type=int, default=2)
    parser.add_argument('--lr', type=float, default=1e-4)
    parser.add_argument('--epochs', type=int, default=100)
    parser.add_argument('--checkpoint', type=str, default='pretrained_lstm_speed.pt')
    args = parser.parse_args()

    pretrain_lstm(args)


