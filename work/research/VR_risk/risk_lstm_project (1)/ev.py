import torch
from torch.utils.data import DataLoader
from risk_lstm_project.model import GaussianTrajectoryModel
from risk_lstm_project.losses import bivariate_gaussian_nll
from risk_lstm_project.sample_and_score import sample_trajectories
from vr_dataset import VRPedestrianDataset
import matplotlib.pyplot as plt

def train_model(csv_path, epochs=20, batch_size=32, lr=0.001):
    dataset = VRPedestrianDataset(csv_path)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = GaussianTrajectoryModel()
    model.to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)

    for epoch in range(epochs):
        total_loss = 0
        model.train()
        for obs, target in dataloader:
            obs = obs.to(device)
            target = target.to(device)
          

            optimizer.zero_grad()
            mu, sigma, rho = model(obs)
            loss = bivariate_gaussian_nll(mu, sigma, rho, target[:, -1])
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        print(f"Epoch {epoch+1}: Loss = {total_loss / len(dataloader):.4f}")
    torch.save(model.state_dict(), "risk_lstm_model.pt")
    return model

def evaluate_model(model, csv_path, num_samples=20):
    dataset = VRPedestrianDataset(csv_path)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)
    model.eval()
    total_ade = 0
    total_fde = 0
    count = 0
    with torch.no_grad():
        for obs, target in dataloader:
            obs = obs.to(model.fc_mu.weight.device)
            target = target.to(model.fc_mu.weight.device)
            mu, sigma, rho = model(obs)
            samples = sample_trajectories(mu, sigma, rho, num_samples=num_samples)
            pred = samples.mean(dim=1)
            ade = ((pred - target) ** 2).sum(dim=-1).sqrt().mean().item()
            fde = ((pred[:, -1] - target[:, -1]) ** 2).sum(dim=-1).sqrt().item()
            total_ade += ade
            total_fde += fde
            count += 1

            # Plot a few examples
            if count <= 5:
                plt.plot(obs[0, :, 0].cpu(), obs[0, :, 1].cpu(), 'go--', label='Obs')
                plt.plot(target[0, :, 0].cpu(), target[0, :, 1].cpu(), 'r--', label='GT')
                plt.plot(pred[0, :, 0].cpu(), pred[0, :, 1].cpu(), 'b--', label='Pred')
                plt.legend()
                plt.title(f"Sample {count} - Risk-Aware Prediction")
                plt.grid(True)
                plt.show()

    print(f"Average ADE: {total_ade / count:.4f}")
    print(f"Average FDE: {total_fde / count:.4f}")
if __name__ == '__main__':
    model = train_model("data/vr_dataset.csv")
    evaluate_model(model, "data/vr_dataset.csv")