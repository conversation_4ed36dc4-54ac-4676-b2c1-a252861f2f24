import os
import math
import argparse
import ast
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from pred_lstm import LSTMGaussianPredictor
from risk_model import RiskModel
from sampler import sample_candidates_gaussian


def encode_brake(bp: str):
    """
    Encode AV brake behavior string into type, intensity, stop distance
    """
    if 'AV_DRIVES_THROUGH' in bp:
        return 0.0, 0.0, 0.0
    intensity = 1.0 if 'STRONG_BRAKE_1ST' in bp else 2.0 if 'STRONG_BRAKE_2ND' in bp else 0.0
    btype = 1.0 if 'BRAKE_LINEAR' in bp else 2.0 if 'BRAKE_2STAGES' in bp else 0.0
    stop_map = {'EARLYSTOP':10.0, 'MEDIUMSTOP':5.0, 'LATESTOP':2.0}
    sd = stop_map.get(bp.split('_')[-1], 0.0)
    return btype, intensity, sd


def load_full_states(csv_file):
    """
    Load a single CSV into a list of full states:
    each state = (p_ped (2,), v_ped, p_AV (2,), v_AV, a_AV, b_type, b_intensity, s_stop, TAV)
    """
    df = pd.read_csv(csv_file)
    # parse arrays
    ped_pos = df['ped_position'].apply(ast.literal_eval).tolist()
    ped_speed = df['ped_speed'].values.astype(float)
    av_pos = df['AV_pos'].apply(ast.literal_eval).tolist()
    av_speed = df['AV_speed'].values.astype(float)
    av_acc = (df['AV_speed'].diff() / df['timestamp'].diff()).fillna(0).values.astype(float)
    b_profile = df['AV_Brake_Behaviour'].tolist()
    gaze = df['T_TAV'].fillna(0).values.astype(float)

    states = []
    for i in range(len(df)-1):
        bt, bi, sd = encode_brake(b_profile[i])
        states.append(
            (np.array(ped_pos[i],dtype=np.float32),
             float(ped_speed[i]),
             np.array(av_pos[i], dtype=np.float32),
             float(av_speed[i]),
             float(av_acc[i]),
             bt, bi, sd,
             float(gaze[i]))
        )
    return states


def compute_ade_fde(gt: np.ndarray, pred: np.ndarray):
    dists = np.linalg.norm(gt - pred, axis=1)
    ADE = dists.mean()
    FDE = dists[-1]
    return ADE, FDE


def main(args):
    device = torch.device(args.device)
    # 1) load full trajectory
    states = load_full_states(args.csv)
    N = len(states)
    if N < args.obs_len + 1:
        raise ValueError("Not enough frames for the given obs_len")

    # 2) load models
    lstm = LSTMGaussianPredictor(in_size=3, hidden_size=args.hidden, num_layers=args.layers).to(device)
    lstm.load_state_dict(torch.load(args.lstm_ckpt, map_location=device))
    lstm.eval()
    risk = RiskModel(
        intended_path_csv=args.intended_path,
        feasible_region_npy=args.feasible,
        A=args.A, alpha=args.alpha,
        B=args.B,
        C=args.C, beta=args.beta, gamma=args.gamma,
        D=args.D, k=args.k,
        E=args.E
    ).to(device)
    risk.load_state_dict(torch.load(args.risk_ckpt))
    risk.eval()

    # storage
    actual = []  # actual ped positions
    pred = []    # predicted ped positions
    risk_actual = []
    risk_pred = []

    # initialize history
    hist_states = states[:args.obs_len]
    for t in range(args.obs_len, N):
        # build history tensor
        hist = torch.tensor(
            [[h[0][0], h[0][1], h[1]] for h in hist_states],
            dtype=torch.float32, device=device
        ).unsqueeze(0)  # (1, obs_len, 3)

        # groundtruth next
        next_state = states[t]
        actual.append(next_state[0])

        # LSTM predict Δx,Δy and speed
        with torch.no_grad():
            mu_dxdy, mu_speed, log_var = lstm(hist)
        x_last = hist[0,-1,:2]
        # reconstruct predicted state
        next_pred_pos = (x_last + mu_dxdy[0]).cpu().numpy()
        next_pred_speed = mu_speed[0].cpu().numpy()[0]
        pred.append(next_pred_pos)

        # build candidate_states for both actual and pred (K=1)
        # we need AV features and gaze from next_state's tuple
        p_ped, v_ped, p_AV, v_AV, a_AV, bt, bi, sd, TAV = next_state
        # prepare tensors
        ped_c = torch.tensor(next_pred_pos, device=device).view(1,2)
        cand_actual = torch.tensor(next_state[0], device=device).view(1,2)
        av_dict = {
            'AV_pos': torch.tensor(p_AV, device=device).view(1,2),
            'v_AV': torch.tensor(v_AV, device=device).view(1),
            'a_AV': torch.tensor(a_AV, device=device).view(1),
            'b_type': torch.tensor(bt, device=device).view(1),
            'b_intensity': torch.tensor(bi, device=device).view(1),
            's_stop': torch.tensor(sd, device=device).view(1)
        }
        gaze = torch.tensor(TAV, device=device).view(1)
        # action_params: velocity magnitude & 0 heading
        with torch.no_grad():
            vel_actual = torch.norm(cand_actual - x_last.unsqueeze(0), dim=1)/args.delta_t
            vel_pred   = torch.norm(ped_c        - x_last.unsqueeze(0), dim=1)/args.delta_t
        action_actual = torch.stack([vel_actual, torch.zeros_like(vel_actual)], dim=1).unsqueeze(0)
        action_pred   = torch.stack([vel_pred,   torch.zeros_like(vel_pred)],   dim=1).unsqueeze(0)

        # assemble candidate_states
        cand_states_act = {k: v.unsqueeze(0) for k,v in av_dict.items()}
        cand_states_act['ped_pos'] = cand_actual.unsqueeze(0)
        cand_states_pred = cand_states_act.copy()
        cand_states_pred['ped_pos'] = ped_c.unsqueeze(0)
        gaze_K = gaze.unsqueeze(0)

        # compute risk
        with torch.no_grad():
            R_act = risk(cand_states_act, action_actual, gaze_K)
            R_pr  = risk(cand_states_pred, action_pred,   gaze_K)
        risk_actual.append(R_act.item())
        risk_pred.append(R_pr.item())

        # advance history (teacher forcing): use actual next state
        hist_states.pop(0)
        hist_states.append(next_state)

    actual = np.array(actual)
    pred   = np.array(pred)
    # compute ADE/FDE
    ADE, FDE = compute_ade_fde(actual, pred)
    print(f"Sanity Test ADE: {ADE:.4f}, FDE: {FDE:.4f}")

    # plot trajectory
    plt.figure()
    plt.plot(actual[:,0], actual[:,1], '-o', label='Actual')
    plt.plot(pred[:,0],   pred[:,1],   '-x', label='Predicted')
    plt.legend(); plt.title('Trajectory')
    plt.savefig('test_traj.png')

    # plot risk
    plt.figure()
    plt.plot(risk_actual, label='Risk(Actual)')
    plt.plot(risk_pred,   label='Risk(Pred)')
    plt.xlabel('Step'); plt.ylabel('Risk'); plt.legend()
    plt.savefig('test_risk.png')


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--csv', default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data/Paricipant1_1_processed.csv', help='Single trial CSV file')
    parser.add_argument('--data_dir', help='ignored')
    parser.add_argument('--risk_ckpt', default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/risky_trained.pt')
    parser.add_argument('--lstm_ckpt', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/pretrained_lstm_speed.pt')
    parser.add_argument('--intended_path', type=str,default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/intended_path.csv')
    parser.add_argument('--feasible', type=str, default='/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/feasible_corridor_polygon.npy')
    parser.add_argument('--obs_len', type=int, default=10)
    parser.add_argument('--delta_t', type=float, default=0.1)
    parser.add_argument('--hidden', type=int, default=64)
    parser.add_argument('--layers', type=int, default=2)
    parser.add_argument('--A', type=float, default=1.0)
    parser.add_argument('--alpha', type=float, default=1.0)
    parser.add_argument('--B', type=float, default=1.0)
    parser.add_argument('--C', type=float, default=1.0)
    parser.add_argument('--beta', type=float, default=1.0)
    parser.add_argument('--gamma', type=float, default=1.0)
    parser.add_argument('--D', type=float, default=1.0)
    parser.add_argument('--k', type=float, default=1.0)
    parser.add_argument('--E', type=float, default=1.0)
    parser.add_argument('--device', default='cpu')
    args = parser.parse_args()
    main(args)
