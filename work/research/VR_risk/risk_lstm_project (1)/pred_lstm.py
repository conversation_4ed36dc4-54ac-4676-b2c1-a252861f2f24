# import os
# import math
# import ast
# import torch
# import torch.nn as nn
# import torch.optim as optim
# import numpy as np
# import pandas as pd
# from torch.utils.data import Dataset, DataLoader
# from sklearn.metrics import mean_squared_error

# # ─────────────────────────────────────────────────────────────────────────────
# # 1) Dataset and DataLoader
# # ─────────────────────────────────────────────────────────────────────────────

# class PedestrianDataset(Dataset):
#     def __init__(self, records, seq_length=10):
#         self.X, self.y = self._prepare(records, seq_length)

#     def _prepare(self, records, seq_length):
#         X, y = [], []
#         for trial in records:
#             states = trial['states']    # list of [x, y, speed]
#             for i in range(len(states) - seq_length):
#                 seq = states[i:i+seq_length]
#                 tgt = states[i+seq_length][:2]  # only x,y target
#                 X.append(seq)
#                 y.append(tgt)
#         return torch.tensor(X, dtype=torch.float32), torch.tensor(y, dtype=torch.float32)

#     def __len__(self): return len(self.X)
#     def __getitem__(self, i): return self.X[i], self.y[i]

# def load_data_from_csv(data_dir):
#     records = []
#     for fn in sorted(os.listdir(data_dir)):
#         if not fn.endswith('.csv'): continue
#         df = pd.read_csv(os.path.join(data_dir, fn))
#         # assume df has columns 'ped_position' (str "[x,y]") and 'ped_speed'
#         states = []
#         for pos_str, speed in zip(df['ped_position'], df['ped_speed']):
#             xy = ast.literal_eval(pos_str)
#             states.append([xy[0], xy[1], speed])
#         records.append({'states': states})
#     return records

# def split_records(records, train_frac=0.8):
#     np.random.shuffle(records)
#     n = int(len(records)*train_frac)
#     return records[:n], records[n:]


# # ─────────────────────────────────────────────────────────────────────────────
# # 2) Gaussian LSTM Predictor
# # ─────────────────────────────────────────────────────────────────────────────

# class LSTMGaussianPredictor(nn.Module):
#     def __init__(self, in_size=3, hidden_size=64, num_layers=2, dropout=0.2):
#         super().__init__()
#         self.lstm = nn.LSTM(in_size, hidden_size, num_layers,
#                             batch_first=True, dropout=dropout)
#         # now predict: Δx, Δy, speed, log_var_x, log_var_y, log_var_speed
#         self.head = nn.Linear(hidden_size, 6)

#     def forward(self, x):
#         out, _ = self.lstm(x)
#         h = out[:, -1, :]
#         params = self.head(h)        # (B,6)
#         mu_pos   = params[:, :2]     # Δx, Δy
#         mu_speed = params[:, 2:3]    # speed
#         log_var  = params[:, 3:].clamp(-10,10)  # variances for x,y,speed
#         return mu_pos, mu_speed, log_var


# # def gaussian_nll(mu, log_var, target):
# #     """
# #     μ, logσ²: (B,2), target: (B,2)
# #     """
# #     var = torch.exp(log_var)
# #     diff2 = (target - mu).pow(2)
# #     nll = 0.5 * (math.log(2*math.pi) + log_var + diff2/var).sum(dim=1)
# #     return nll.mean()

# def gaussian_nll(mu, log_var, target, epsilon=1e-6):
#     var = torch.exp(log_var) + epsilon
#     diff2 = (target - mu).pow(2)
#     nll = 0.5 * (math.log(2 * math.pi) + log_var + diff2 / var).sum(dim=1)
#     return nll.mean()

# # ─────────────────────────────────────────────────────────────────────────────
# # 3) Train + Evaluate
# # ─────────────────────────────────────────────────────────────────────────────

# def train_and_evaluate(data_dir, seq_len=10, batch_size=32,
#                        lr=1e-4, epochs=100, device='cpu'):
#     # Load & split
#     records = load_data_from_csv(data_dir)
#     train_recs, val_recs = split_records(records)
#     train_ds = PedestrianDataset(train_recs, seq_len)
#     val_ds   = PedestrianDataset(val_recs,   seq_len)
#     train_dl = DataLoader(train_ds, batch_size, shuffle=True)
#     val_dl   = DataLoader(val_ds,   batch_size, shuffle=False)

#     # Model, optimizer
#     model = LSTMGaussianPredictor().to(device)

#     optimizer = optim.Adam(model.parameters(), lr=lr)

#     best_val_rmse = float('inf')
#     for ep in range(1, epochs+1):
#         # --- Train ---
#         model.train()
#         train_loss = 0
#         for Xb, yb in train_dl:
#             Xb, yb = Xb.to(device), yb.to(device)
#             optimizer.zero_grad()
#             mu, log_var = model(Xb)
#             loss = gaussian_nll(mu, log_var, yb)
#             loss.backward()
#             optimizer.step()
#             train_loss += loss.item()
#         train_loss /= len(train_dl)

#         # --- Validation ---
#         model.eval()
#         val_nll = 0.0
#         preds, gts = [], []
#         with torch.no_grad():
#             for Xb, yb in val_dl:
#                 Xb, yb = Xb.to(device), yb.to(device)
#                 mu, log_var = model(Xb)
#                 val_nll += gaussian_nll(mu, log_var, yb).item()
#                 preds.append(mu.cpu().numpy())
#                 gts.append(yb.cpu().numpy())

#         val_nll /= len(val_dl)
#         preds = np.vstack(preds)
#         gts   = np.vstack(gts)
#         val_rmse = math.sqrt(mean_squared_error(gts, preds))

#         # Save best
#         if val_rmse < best_val_rmse:
#             best_val_rmse = val_rmse
#             torch.save(model.state_dict(), 'best_lstm_gaussian.pth')

#         # Log every 10 epochs
#         if ep % 10 == 0 or ep == 1:
#             print(f"Epoch {ep:03d} | Train NLL: {train_loss:.4f} "
#                   f"| Val NLL: {val_nll:.4f} | Val RMSE: {val_rmse:.4f}")

#     # Final evaluation load best
#     model.load_state_dict(torch.load('best_lstm_gaussian.pth', map_location=device))
#     model.eval()
#     all_preds, all_gts = [], []
#     with torch.no_grad():
#         for Xb, yb in val_dl:
#             Xb = Xb.to(device)
#             mu, _ = model(Xb)
#             all_preds.append(mu.cpu().numpy())
#             all_gts.append(yb.numpy())
#     all_preds = np.vstack(all_preds)
#     all_gts   = np.vstack(all_gts)
#     final_rmse = math.sqrt(mean_squared_error(all_gts, all_preds))
#     print(f"\nFinal Validation RMSE = {final_rmse:.4f}")

#     return model

# ─────────────────────────────────────────────────────────────────────────────
# 4) Entry Point
# ─────────────────────────────────────────────────────────────────────────────
import os
import math
import ast
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import mean_squared_error

# ─────────────────────────────────────────────────────────────────────────────
# 1) Dataset and DataLoader (with speed)
# ─────────────────────────────────────────────────────────────────────────────
class PedestrianDataset(Dataset):
    """
    Each record: sequence of [x, y, speed] states.
    Targets: next [x, y, speed].
    """
    def __init__(self, records, seq_length=10):
        self.X, self.y = self._prepare(records, seq_length)

    def _prepare(self, records, seq_length):
        X, y = [], []
        for trial in records:
            states = trial['states']
            for i in range(len(states) - seq_length):
                seq = states[i:i+seq_length]
                tgt = states[i+seq_length]
                X.append(seq)
                y.append(tgt)
        return torch.tensor(X, dtype=torch.float32), torch.tensor(y, dtype=torch.float32)

    def __len__(self): return len(self.X)
    def __getitem__(self, i): return self.X[i], self.y[i]


def load_data_from_csv(data_dir):
    records = []
    for fn in sorted(os.listdir(data_dir)):
        if not fn.endswith('.csv'): continue
        df = pd.read_csv(os.path.join(data_dir, fn))
        states = []
        for pos_str, speed in zip(df['ped_position'], df['ped_speed']):
            xy = ast.literal_eval(pos_str)
            states.append([xy[0], xy[1], speed])
        records.append({'states': states})
    return records


def split_records(records, train_frac=0.8):
    np.random.shuffle(records)
    n = int(len(records) * train_frac)
    return records[:n], records[n:]

# ─────────────────────────────────────────────────────────────────────────────
# 2) Gaussian LSTM Predictor (predicting Δx, Δy, speed)
# ─────────────────────────────────────────────────────────────────────────────
class LSTMGaussianPredictor(nn.Module):
    def __init__(self, in_size=3, hidden_size=64, num_layers=2, dropout=0.2):
        super().__init__()
        self.lstm = nn.LSTM(in_size, hidden_size, num_layers,
                            batch_first=True, dropout=dropout)
        # outputs: Δx, Δy, speed ; log_var_x, log_var_y, log_var_speed
        self.head = nn.Linear(hidden_size, 6)

    def forward(self, x):
        out, _ = self.lstm(x)
        h = out[:, -1, :]
        params = self.head(h)
        mu_dxdy  = params[:, :2]
        mu_speed = params[:, 2:3]
        log_var  = params[:, 3:].clamp(min=-10, max=10)
        return mu_dxdy, mu_speed, log_var


def gaussian_nll(mu, log_var, target, epsilon=1e-6):
    var = torch.exp(log_var) + epsilon
    diff2 = (target - mu).pow(2)
    nll = 0.5 * ((math.log(2*math.pi) + log_var) + diff2/var).sum(dim=1)
    return nll.mean()

# ─────────────────────────────────────────────────────────────────────────────
# 3) Train + Evaluate (with speed metrics)
# ─────────────────────────────────────────────────────────────────────────────
def train_and_evaluate(data_dir, seq_len=10, batch_size=32,
                       lr=1e-4, epochs=100, device='cpu'):
    # Load & split
    records = load_data_from_csv(data_dir)
    train_recs, val_recs = split_records(records)
    train_ds = PedestrianDataset(train_recs, seq_len)
    val_ds   = PedestrianDataset(val_recs,   seq_len)
    train_dl = DataLoader(train_ds, batch_size, shuffle=True)
    val_dl   = DataLoader(val_ds,   batch_size, shuffle=False)

    # Model & optimizer
    model = LSTMGaussianPredictor().to(device)
    optimizer = optim.Adam(model.parameters(), lr=lr)
    best_val_rmse = float('inf')

    for ep in range(1, epochs+1):
        model.train()
        train_loss = 0.0
        for Xb, yb in train_dl:
            Xb, yb = Xb.to(device), yb.to(device)
            optimizer.zero_grad()
            mu_dxdy, mu_speed, log_var = model(Xb)
            mu = torch.cat([mu_dxdy, mu_speed], dim=1)
            loss = gaussian_nll(mu, log_var, yb)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        train_loss /= len(train_dl)

        # Validation
        model.eval()
        all_preds, all_gts = [], []
        with torch.no_grad():
            for Xb, yb in val_dl:
                Xb, yb = Xb.to(device), yb.to(device)
                mu_dxdy, mu_speed, _ = model(Xb)
                mu = torch.cat([mu_dxdy, mu_speed], dim=1)
                all_preds.append(mu.cpu().numpy())
                all_gts.append(yb.cpu().numpy())
        all_preds = np.vstack(all_preds)
        all_gts   = np.vstack(all_gts)

        # Position metrics
        pos_pred = all_preds[:, :2]
        pos_gt   = all_gts[:, :2]
        pos_rmse = math.sqrt(mean_squared_error(pos_gt, pos_pred))
        pos_ade  = np.mean(np.linalg.norm(pos_pred - pos_gt, axis=1))

        # Speed metrics
        speed_pred = all_preds[:, 2]
        speed_gt   = all_gts[:, 2]
        speed_rmse = math.sqrt(mean_squared_error(speed_gt, speed_pred))
        speed_ade  = np.mean(np.abs(speed_pred - speed_gt))

        # Log
        if ep % 10 == 0 or ep == 1:
            print(f"Epoch {ep:03d} | Train NLL: {train_loss:.4f} | "
                  f"Pos RMSE: {pos_rmse:.4f}, ADE: {pos_ade:.4f} | "
                  f"Speed RMSE: {speed_rmse:.4f}, MAE: {speed_ade:.4f}")

        # Save best by position RMSE
        if pos_rmse < best_val_rmse:
            best_val_rmse = pos_rmse
            torch.save(model.state_dict(), 'best_lstm_speed_eval.pth')

    # Final evaluation
    print(f"Training complete. Best Val Pos RMSE: {best_val_rmse:.4f}")
    return model

# ─────────────────────────────────────────────────────────────────────────────
# 5) Entry Point
# ─────────────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    data_directory = '/home/<USER>/work/aa_postdoc/VR_exp/VR_Lordes/processed_data'
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    trained_model = train_and_evaluate(
        data_directory,
        seq_len=10,
        batch_size=32,
        lr=1e-4,
        epochs=100,
        device=device
    )
