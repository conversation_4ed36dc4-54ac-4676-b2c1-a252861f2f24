#!/usr/bin/env python3
"""
Comprehensive Demo of LSTM-Based Trajectory Prediction with Risk-Aware Action Selection

This script demonstrates the complete trajectory prediction system based on the formulation:

Mathematical Formulation:
1. Joint State: x_t = (x_ped(t), x_AV(t))
2. LSTM Operator: Encoder → Action Embedding → Decoder → State Delta
3. Risk + Cost Scoring: S_j = R(x'_j; p) + C(a_j; q)
4. Choice Probability: P^(a_j|x_t) = exp(-S_j) / Σ_k exp(-S_k)
5. Training Loss: L = -log P^(a_t|x_t)

Usage:
    python comprehensive_demo.py
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import ast
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Device configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Import our custom modules
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler, parse_state_from_data
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel, cross_entropy_loss
from trajectory_inference import TrajectoryInference, TrajectoryEvaluator, create_inference_system
from trajectory_visualization import TrajectoryVisualizer, create_comprehensive_report


def create_synthetic_trajectories(num_trajectories: int = 50,
                                seq_length: int = 30) -> List[Dict]:
    """
    Create synthetic pedestrian-vehicle interaction trajectories.
    """
    print(f"Creating {num_trajectories} synthetic trajectories...")
    trajectories = []

    for i in range(num_trajectories):
        # Initialize random starting positions
        ped_start = np.random.uniform([0, 0], [10, 10])
        av_start = np.random.uniform([20, 5], [30, 15])

        states = []
        actions = []

        # Current state
        ped_pos = ped_start.copy()
        ped_vel = np.random.uniform([-1, -1], [1, 1])
        av_pos = av_start.copy()
        av_speed = np.random.uniform(5, 10)
        av_accel = 0.0

        for t in range(seq_length):
            # Create state vector
            state = np.array([
                ped_pos[0], ped_pos[1],  # ped position
                ped_vel[0], ped_vel[1],  # ped velocity
                av_pos[0], av_pos[1],    # AV position
                av_speed,                # AV speed
                av_accel                 # AV acceleration
            ])
            states.append(state)

            # Generate action (speed and heading change)
            speed_change = np.random.normal(0, 0.5)
            heading_change = np.random.normal(0, 0.2)
            actions.append([speed_change, heading_change])

            # Update state based on action
            dt = 0.1

            # Update pedestrian
            ped_speed = np.linalg.norm(ped_vel)
            ped_heading = np.arctan2(ped_vel[1], ped_vel[0])

            new_speed = max(0, ped_speed + speed_change * dt)
            new_heading = ped_heading + heading_change * dt

            ped_vel = new_speed * np.array([np.cos(new_heading), np.sin(new_heading)])
            ped_pos += ped_vel * dt

            # Update AV (simple constant velocity)
            av_pos[0] -= av_speed * dt  # Moving towards pedestrian
            av_speed = max(0, av_speed + av_accel * dt)

        trajectories.append({
            'states': np.array(states),
            'actions': np.array(actions),
            'file': f'synthetic_{i}'
        })

    return trajectories


def load_vr_data(data_dir: str, max_files: int = 10) -> List[Dict]:
    """
    Load VR data from CSV files and convert to trajectory format.
    """
    data_path = Path(data_dir)
    if not data_path.exists():
        print(f"Data directory {data_dir} not found. Using synthetic data.")
        return []

    csv_files = list(data_path.glob("*.csv"))[:max_files]
    trajectories = []

    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file)

            # Extract relevant columns
            states = []
            actions = []

            for i, row in df.iterrows():
                # Parse pedestrian position
                ped_pos = ast.literal_eval(row['ped_position'])
                ped_speed = row['ped_speed']

                # Parse AV position
                av_pos = ast.literal_eval(row['AV_pos'])
                av_speed = row['AV_speed']

                # Create state vector
                state = parse_state_from_data(
                    ped_pos=ped_pos,
                    ped_speed=ped_speed,
                    av_pos=av_pos,
                    av_speed=av_speed
                )

                states.append(state.numpy())

                # Compute action (speed and heading change)
                if i > 0:
                    prev_speed = np.linalg.norm(states[i-1][2:4])
                    curr_speed = np.linalg.norm(state[2:4].numpy())
                    speed_change = curr_speed - prev_speed

                    # Simplified heading change (would need proper computation)
                    heading_change = np.random.normal(0, 0.1)  # Placeholder

                    actions.append([speed_change, heading_change])

            if len(states) > 10:  # Minimum sequence length
                trajectories.append({
                    'states': np.array(states),
                    'actions': np.array(actions),
                    'file': csv_file.name
                })

        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue

    print(f"Loaded {len(trajectories)} trajectories from {len(csv_files)} files")
    return trajectories


def initialize_models(config: Dict) -> Tuple:
    """Initialize all model components."""
    print("Initializing models...")

    # Initialize components
    trajectory_predictor = LSTMTrajectoryPredictor(
        state_dim=config['state_dim'],
        hidden_dim=config['hidden_dim'],
        num_layers=config['num_layers'],
        action_embed_dim=config['action_embed_dim'],
        dropout=config['dropout']
    ).to(device)

    risk_model = EnhancedRiskModel(
        collision_weight=1.0,
        path_deviation_weight=0.5,
        behavioral_weight=0.3,
        perceptual_weight=0.2
    ).to(device)

    action_sampler = ActionSampler(
        speed_range=(-2.0, 2.0),
        heading_range=(-np.pi/4, np.pi/4),
        num_speed_bins=5,
        num_heading_bins=5
    )

    choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model).to(device)

    # Create inference system
    inference_system = create_inference_system(
        trajectory_predictor=trajectory_predictor,
        risk_model=risk_model,
        action_sampler=action_sampler,
        horizon=config['horizon'],
        selection_mode='deterministic'
    )

    print(f"Models initialized on {device}")
    print(f"Number of candidate actions: {action_sampler.num_actions}")
    print(f"Trajectory predictor parameters: {sum(p.numel() for p in trajectory_predictor.parameters()):,}")
    print(f"Risk model parameters: {sum(p.numel() for p in risk_model.parameters()):,}")

    return trajectory_predictor, risk_model, action_sampler, choice_model, inference_system


def demo_single_step_prediction(choice_model, action_sampler, trajectories, config):
    """Demonstrate single-step trajectory prediction with risk analysis."""
    print("\n" + "="*60)
    print("SINGLE-STEP PREDICTION DEMO")
    print("="*60)

    # Prepare sample data
    sample_traj = trajectories[0]
    states = torch.tensor(sample_traj['states'], dtype=torch.float32).to(device)

    # Create sequence for prediction
    seq_len = config['sequence_length']
    start_idx = 10

    state_history = states[start_idx:start_idx+seq_len].unsqueeze(0)  # (1, T, 8)
    current_state = states[start_idx+seq_len].unsqueeze(0)  # (1, 8)

    print(f"State history shape: {state_history.shape}")
    print(f"Current state shape: {current_state.shape}")

    # Get candidate actions
    candidate_actions = action_sampler.get_candidates(device)
    print(f"Candidate actions shape: {candidate_actions.shape}")

    # Single-step prediction
    with torch.no_grad():
        # Predict states for all actions
        predicted_states, state_deltas = choice_model.trajectory_predictor(
            state_history, candidate_actions, current_state
        )

        # Compute probabilities and scores
        probabilities, scores = choice_model(
            state_history, current_state, candidate_actions
        )

        # Select best action
        selected_action_idx = torch.argmax(probabilities[:, 0]).item()
        selected_action = candidate_actions[selected_action_idx]

    print(f"\nPrediction Results:")
    print(f"  Predicted states shape: {predicted_states.shape}")
    print(f"  Probabilities shape: {probabilities.shape}")
    print(f"  Selected action index: {selected_action_idx}")
    print(f"  Selected action: {selected_action.cpu().numpy()}")
    print(f"  Action probability: {probabilities[selected_action_idx, 0].item():.4f}")

    # Analyze risk components
    print(f"\nRisk Analysis:")
    risk_scores = scores[:, 0].cpu().numpy()
    action_probs = probabilities[:, 0].cpu().numpy()

    print(f"  Risk score range: [{risk_scores.min():.3f}, {risk_scores.max():.3f}]")
    print(f"  Probability range: [{action_probs.min():.3f}, {action_probs.max():.3f}]")
    print(f"  Selected action risk: {risk_scores[selected_action_idx]:.3f}")

    # Visualize single-step prediction
    visualizer = TrajectoryVisualizer()
    visualizer.plot_single_step_prediction(
        state_history=state_history,
        current_state=current_state,
        predicted_states=predicted_states,
        probabilities=probabilities,
        scores=scores,
        candidate_actions=candidate_actions,
        selected_action_idx=selected_action_idx,
        batch_idx=0
    )

    return state_history, current_state


def demo_multi_step_prediction(inference_system, state_history, current_state, config):
    """Demonstrate multi-step trajectory prediction."""
    print("\n" + "="*60)
    print("MULTI-STEP TRAJECTORY PREDICTION DEMO")
    print("="*60)

    # Multi-step trajectory prediction
    print(f"Predicting trajectory for {config['horizon']} steps...")

    with torch.no_grad():
        trajectory = inference_system.predict_trajectory(
            state_history, current_state
        )

    print(f"Trajectory prediction completed!")
    print(f"  Predicted states shape: {trajectory.states.shape}")
    print(f"  Selected actions shape: {trajectory.actions.shape}")
    print(f"  Average confidence: {trajectory.confidence.mean().item():.4f}")
    print(f"  Min confidence: {trajectory.confidence.min().item():.4f}")
    print(f"  Max confidence: {trajectory.confidence.max().item():.4f}")

    # Visualize multi-step trajectory
    visualizer = TrajectoryVisualizer()
    visualizer.plot_multi_step_trajectory(
        trajectory_prediction=trajectory,
        initial_state_history=state_history,
        batch_idx=0
    )

    return trajectory


def demo_stochastic_prediction(inference_system, state_history, current_state, config):
    """Demonstrate stochastic trajectory prediction with multiple samples."""
    print("\n" + "="*60)
    print("STOCHASTIC TRAJECTORY PREDICTION DEMO")
    print("="*60)

    # Switch to stochastic mode
    inference_system.selection_mode = 'stochastic'
    inference_system.temperature = 0.5

    print(f"Generating multiple trajectory samples...")
    print(f"  Selection mode: {inference_system.selection_mode}")
    print(f"  Temperature: {inference_system.temperature}")

    # Generate multiple samples
    num_samples = 10
    with torch.no_grad():
        trajectories = inference_system.predict_multiple_trajectories(
            state_history, current_state, num_samples=num_samples
        )

    print(f"Generated {len(trajectories)} trajectory samples")

    # Analyze sample diversity
    final_positions = []
    avg_confidences = []

    for i, traj in enumerate(trajectories):
        final_pos = traj.states[-1, 0, :2].cpu().numpy()  # Final pedestrian position
        avg_conf = traj.confidence[:, 0].mean().item()

        final_positions.append(final_pos)
        avg_confidences.append(avg_conf)

        print(f"  Sample {i+1}: Final pos = [{final_pos[0]:.2f}, {final_pos[1]:.2f}], "
              f"Avg confidence = {avg_conf:.3f}")

    final_positions = np.array(final_positions)
    position_std = np.std(final_positions, axis=0)

    print(f"\nSample Diversity:")
    print(f"  Position std: [{position_std[0]:.3f}, {position_std[1]:.3f}]")
    print(f"  Confidence std: {np.std(avg_confidences):.3f}")

    # Visualize multiple trajectories
    visualizer = TrajectoryVisualizer()
    visualizer.plot_multiple_trajectories(
        trajectories=trajectories,
        initial_state_history=state_history,
        batch_idx=0
    )

    return trajectories


def demo_training_simulation(choice_model, trajectories, config):
    """Simulate training process with synthetic ground truth."""
    print("\n" + "="*60)
    print("TRAINING SIMULATION DEMO")
    print("="*60)

    # Prepare training data
    optimizer = optim.Adam(choice_model.parameters(), lr=config['learning_rate'])

    # Create mini-batch from trajectories
    batch_states = []
    batch_actions = []

    for traj in trajectories[:config['batch_size']]:
        states = torch.tensor(traj['states'], dtype=torch.float32)
        actions = torch.tensor(traj['actions'], dtype=torch.float32)

        if len(states) > config['sequence_length'] + 1:
            batch_states.append(states)
            batch_actions.append(actions)

    if len(batch_states) == 0:
        print("No suitable training data found.")
        return

    print(f"Training on {len(batch_states)} trajectories...")

    # Simulate training steps
    num_epochs = 5
    losses = []

    for epoch in range(num_epochs):
        epoch_loss = 0.0

        for i, (states, actions) in enumerate(zip(batch_states, batch_actions)):
            if len(states) < config['sequence_length'] + 2:
                continue

            # Prepare sequence
            seq_len = config['sequence_length']
            start_idx = np.random.randint(0, len(states) - seq_len - 1)

            state_history = states[start_idx:start_idx+seq_len].unsqueeze(0).to(device)
            current_state = states[start_idx+seq_len].unsqueeze(0).to(device)

            # Get candidate actions
            candidate_actions = choice_model.trajectory_predictor.action_embedding.action_sampler.get_candidates(device)

            # Forward pass
            probabilities, scores = choice_model(state_history, current_state, candidate_actions)

            # Create dummy ground truth (random action for simulation)
            gt_action_idx = torch.randint(0, len(candidate_actions), (1,)).to(device)

            # Compute loss
            loss = cross_entropy_loss(probabilities, gt_action_idx)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            epoch_loss += loss.item()

        avg_loss = epoch_loss / len(batch_states)
        losses.append(avg_loss)

        print(f"  Epoch {epoch+1}/{num_epochs}: Loss = {avg_loss:.4f}")

    # Plot training curve
    plt.figure(figsize=(8, 5))
    plt.plot(range(1, num_epochs+1), losses, 'o-', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Cross-Entropy Loss')
    plt.title('Training Simulation')
    plt.grid(True, alpha=0.3)
    plt.show()

    print(f"Training simulation completed!")
    print(f"  Final loss: {losses[-1]:.4f}")
    print(f"  Loss reduction: {((losses[0] - losses[-1]) / losses[0] * 100):.1f}%")


def main():
    """Main demonstration function."""
    print("="*80)
    print("LSTM-BASED TRAJECTORY PREDICTION WITH RISK-AWARE ACTION SELECTION")
    print("="*80)
    print()
    print("This demo showcases the complete trajectory prediction system based on:")
    print("1. Joint State Definition: x_t = (x_ped(t), x_AV(t))")
    print("2. LSTM Encoder-Decoder with Action Embedding")
    print("3. Multi-term Risk Field: R(x'_j; p)")
    print("4. Action Cost: C(a_j; q)")
    print("5. Choice Probability: P^(a_j|x_t) = exp(-S_j) / Σ_k exp(-S_k)")
    print("6. End-to-End Training with Cross-Entropy Loss")
    print()

    # Configuration
    config = {
        'state_dim': 8,
        'hidden_dim': 64,
        'num_layers': 2,
        'action_embed_dim': 32,
        'dropout': 0.1,
        'sequence_length': 10,
        'horizon': 15,
        'learning_rate': 1e-3,
        'batch_size': 16
    }

    print("Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()

    # Load or create data
    print("Loading data...")
    trajectories = load_vr_data("processed_data", max_files=20)

    if not trajectories:
        trajectories = create_synthetic_trajectories(num_trajectories=50)

    # Display data statistics
    lengths = [len(traj['states']) for traj in trajectories]
    print(f"Data Statistics:")
    print(f"  Number of trajectories: {len(trajectories)}")
    print(f"  Average length: {np.mean(lengths):.1f} steps")
    print(f"  Length range: {min(lengths)} - {max(lengths)} steps")

    # Visualize sample trajectory
    sample_traj = trajectories[0]
    states = sample_traj['states']

    plt.figure(figsize=(12, 5))

    # Plot trajectory
    plt.subplot(1, 2, 1)
    plt.plot(states[:, 0], states[:, 1], 'o-', label='Pedestrian', alpha=0.7)
    plt.plot(states[:, 4], states[:, 5], 's-', label='Vehicle', alpha=0.7)
    plt.xlabel('X Position (m)')
    plt.ylabel('Y Position (m)')
    plt.title('Sample Trajectory')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axis('equal')

    # Plot speeds
    plt.subplot(1, 2, 2)
    ped_speeds = np.linalg.norm(states[:, 2:4], axis=1)
    av_speeds = states[:, 6]
    plt.plot(ped_speeds, label='Pedestrian Speed', alpha=0.7)
    plt.plot(av_speeds, label='Vehicle Speed', alpha=0.7)
    plt.xlabel('Time Step')
    plt.ylabel('Speed (m/s)')
    plt.title('Speed Profiles')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Initialize models
    trajectory_predictor, risk_model, action_sampler, choice_model, inference_system = initialize_models(config)

    # Run demonstrations
    try:
        # 1. Single-step prediction
        state_history, current_state = demo_single_step_prediction(
            choice_model, action_sampler, trajectories, config
        )

        # 2. Multi-step prediction
        trajectory = demo_multi_step_prediction(
            inference_system, state_history, current_state, config
        )

        # 3. Stochastic prediction
        stochastic_trajectories = demo_stochastic_prediction(
            inference_system, state_history, current_state, config
        )

        # 4. Training simulation
        demo_training_simulation(choice_model, trajectories, config)

        # 5. Create animation (optional)
        print("\n" + "="*60)
        print("TRAJECTORY ANIMATION")
        print("="*60)

        visualizer = TrajectoryVisualizer()
        print("Creating trajectory animation...")
        anim = visualizer.create_trajectory_animation(
            trajectory_prediction=trajectory,
            initial_state_history=state_history,
            batch_idx=0
        )

        print("\nDemo completed successfully!")
        print("\nKey Features Demonstrated:")
        print("✓ LSTM-based trajectory prediction")
        print("✓ Risk-aware action selection")
        print("✓ Multi-step rollout prediction")
        print("✓ Stochastic trajectory sampling")
        print("✓ End-to-end training simulation")
        print("✓ Comprehensive visualization")
        print("✓ Interactive trajectory animation")

    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)

    # Run the comprehensive demo
    main()