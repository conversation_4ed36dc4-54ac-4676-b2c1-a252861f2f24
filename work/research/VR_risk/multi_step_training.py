"""
End-to-end training for multi-step trajectory prediction.
This is OPTIONAL - the multi-step generator works without additional training.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from typing import Tuple, Optional

from multi_step_trajectory_generator import MultiStepTrajectoryGenerator
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel


class MultiStepTrainer:
    """
    OPTIONAL: End-to-end trainer for multi-step trajectory prediction.
    
    Note: The multi-step generator works fine without this additional training!
    This is only for optimizing the full multi-step performance.
    """
    
    def __init__(self, 
                 multi_step_generator: MultiStepTrajectoryGenerator,
                 device: torch.device,
                 learning_rate: float = 1e-4):
        """
        Initialize multi-step trainer.
        
        Args:
            multi_step_generator: Multi-step trajectory generator
            device: Device to train on
            learning_rate: Learning rate (lower than single-step)
        """
        self.multi_step_generator = multi_step_generator
        self.device = device
        self.learning_rate = learning_rate
        
        # Move to device
        self.multi_step_generator.to(device)
        
        # Optimizer for all parameters
        self.optimizer = optim.Adam(
            self.multi_step_generator.parameters(),
            lr=learning_rate,
            weight_decay=1e-6
        )
        
        print(f"🔧 Multi-step trainer initialized")
        print(f"  Learning rate: {learning_rate}")
        print(f"  Total parameters: {sum(p.numel() for p in self.multi_step_generator.parameters()):,}")
    
    def compute_multi_step_loss(self, 
                               obs_seq: torch.Tensor,
                               pred_seq: torch.Tensor) -> torch.Tensor:
        """
        Compute loss for multi-step trajectory prediction.
        
        Args:
            obs_seq: Observation sequence [B, obs_len, state_dim]
            pred_seq: Ground truth prediction sequence [B, pred_len, state_dim]
            
        Returns:
            Multi-step loss
        """
        batch_size = obs_seq.shape[0]
        current_state = obs_seq[:, -1]  # [B, state_dim]
        
        # Generate best trajectories
        best_trajectories, all_trajectories = self.multi_step_generator(
            state_history=obs_seq,
            current_state=current_state,
            selection_criteria='min_risk'
        )
        
        total_loss = 0.0
        
        for b in range(batch_size):
            best_traj = best_trajectories[b]
            target_states = pred_seq[b]  # [pred_len, state_dim]
            
            # Trajectory reconstruction loss (MSE on positions)
            pred_positions = best_traj.states[:, :2]  # [horizon, 2]
            target_positions = target_states[:, :2]   # [pred_len, 2]
            
            # Handle different sequence lengths
            min_len = min(pred_positions.shape[0], target_positions.shape[0])
            position_loss = nn.MSELoss()(
                pred_positions[:min_len], 
                target_positions[:min_len]
            )
            
            # Risk regularization (encourage low-risk trajectories)
            risk_loss = best_traj.total_risk * 0.1
            
            # Action smoothness (encourage smooth actions)
            if best_traj.actions.shape[0] > 1:
                action_diff = torch.diff(best_traj.actions, dim=0)
                smoothness_loss = torch.mean(torch.norm(action_diff, dim=1)) * 0.01
            else:
                smoothness_loss = 0.0
            
            step_loss = position_loss + risk_loss + smoothness_loss
            total_loss += step_loss
        
        return total_loss / batch_size
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """Train for one epoch."""
        self.multi_step_generator.train()
        total_loss = 0.0
        num_batches = 0
        
        for obs_seq, pred_seq, metadata in train_loader:
            obs_seq = obs_seq.to(self.device)
            pred_seq = pred_seq.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            loss = self.compute_multi_step_loss(obs_seq, pred_seq)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.multi_step_generator.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if num_batches % 10 == 0:
                print(f"  Batch {num_batches}: Loss = {loss.item():.4f}")
        
        return total_loss / num_batches
    
    def validate(self, val_loader: DataLoader) -> float:
        """Validate the multi-step model."""
        self.multi_step_generator.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for obs_seq, pred_seq, metadata in val_loader:
                obs_seq = obs_seq.to(self.device)
                pred_seq = pred_seq.to(self.device)
                
                loss = self.compute_multi_step_loss(obs_seq, pred_seq)
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def train(self, 
              train_loader: DataLoader,
              val_loader: DataLoader,
              num_epochs: int = 10) -> dict:
        """
        Train the multi-step system end-to-end.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            num_epochs: Number of epochs (fewer than single-step)
            
        Returns:
            Training history
        """
        print(f"\n🚀 TRAINING MULTI-STEP SYSTEM END-TO-END")
        print(f"=" * 50)
        print(f"Note: This is OPTIONAL - the system works without this training!")
        print(f"Epochs: {num_epochs} (fewer than single-step training)")
        
        train_losses = []
        val_losses = []
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # Train
            train_loss = self.train_epoch(train_loader)
            
            # Validate
            val_loss = self.validate(val_loader)
            
            train_losses.append(train_loss)
            val_losses.append(val_loss)
            
            print(f"  Train Loss: {train_loss:.4f}")
            print(f"  Val Loss: {val_loss:.4f}")
        
        history = {
            'train_losses': train_losses,
            'val_losses': val_losses
        }
        
        print(f"\n✅ Multi-step training completed!")
        return history


def demonstrate_training_options():
    """Demonstrate the different training options."""
    print("🎯 MULTI-STEP TRAJECTORY TRAINING OPTIONS")
    print("=" * 60)
    
    print("\n📋 OPTION 1: NO ADDITIONAL TRAINING (Recommended)")
    print("-" * 50)
    print("✅ Use pre-trained LSTM + Risk Model")
    print("✅ Multi-step generator works immediately")
    print("✅ Fast inference")
    print("✅ Good performance out-of-the-box")
    print("🎯 Usage: Just use MultiStepTrajectoryGenerator directly")
    
    print("\n📋 OPTION 2: END-TO-END MULTI-STEP TRAINING (Optional)")
    print("-" * 50)
    print("🔧 Train the entire multi-step pipeline")
    print("🔧 Optimizes for multi-step performance specifically")
    print("🔧 May improve trajectory quality")
    print("⚠️  Requires additional training time")
    print("🎯 Usage: Use MultiStepTrainer for fine-tuning")
    
    print("\n💡 RECOMMENDATION:")
    print("Start with Option 1 (no additional training)")
    print("Only use Option 2 if you need to squeeze out extra performance")
    
    print("\n🚀 CURRENT STATUS:")
    print("Your system is ready to use with Option 1!")
    print("The multi-step generator works with your trained models.")


if __name__ == "__main__":
    demonstrate_training_options()
