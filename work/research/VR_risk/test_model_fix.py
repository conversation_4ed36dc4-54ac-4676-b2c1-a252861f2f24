#!/usr/bin/env python3
"""
Test script to verify the model dimension fix.
"""

import torch
import numpy as np
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel

def test_model_forward():
    """Test the model forward pass with correct dimensions."""
    print("🧪 Testing Model Forward Pass")
    print("=" * 40)
    
    try:
        device = torch.device('cpu')
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        # Create test data with correct dimensions
        batch_size = 4
        obs_len = 10
        state_dim = 8
        
        # Test data
        obs_seq = torch.randn(batch_size, obs_len, state_dim)  # [B, T, state_dim]
        current_state = torch.randn(batch_size, state_dim)     # [B, state_dim]
        candidate_actions = action_sampler.get_candidates(device)  # [N, 2]
        
        print(f"Input shapes:")
        print(f"  obs_seq: {obs_seq.shape}")
        print(f"  current_state: {current_state.shape}")
        print(f"  candidate_actions: {candidate_actions.shape}")
        
        # Test forward pass
        with torch.no_grad():
            probabilities, scores = choice_model(obs_seq, current_state, candidate_actions)
        
        print(f"\nOutput shapes:")
        print(f"  probabilities: {probabilities.shape}")
        print(f"  scores: {scores.shape}")
        
        # Check if outputs are valid
        assert probabilities.shape[0] == candidate_actions.shape[0], "Wrong number of actions"
        assert probabilities.shape[1] == batch_size, "Wrong batch size"
        assert torch.allclose(probabilities.sum(dim=0), torch.ones(batch_size), atol=1e-5), "Probabilities don't sum to 1"
        
        print(f"\n✅ Model forward pass successful!")
        print(f"  Probabilities sum to 1: {torch.allclose(probabilities.sum(dim=0), torch.ones(batch_size), atol=1e-5)}")
        print(f"  No NaN values: {not torch.isnan(probabilities).any()}")
        print(f"  No infinite values: {not torch.isinf(probabilities).any()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in model forward pass: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_dimensions():
    """Test the dimensions used in training."""
    print("\n🧪 Testing Training Dimensions")
    print("=" * 40)
    
    try:
        device = torch.device('cpu')
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        # Simulate training data dimensions
        batch_size = 32
        obs_len = 10
        pred_len = 10
        state_dim = 8
        
        # Create sample batch (similar to what comes from dataloader)
        obs_seq = torch.randn(batch_size, obs_len, state_dim)
        pred_seq = torch.randn(batch_size, pred_len, state_dim)
        
        print(f"Training data shapes:")
        print(f"  obs_seq: {obs_seq.shape}")
        print(f"  pred_seq: {pred_seq.shape}")
        
        # Test the training loop logic
        candidate_actions = action_sampler.get_candidates(device)
        
        # Simulate one prediction step
        t = 0
        current_state = obs_seq[:, -1]  # Last observation [B, state_dim]
        target_state = pred_seq[:, t]   # Target state [B, state_dim]
        target_action = target_state[:, :2] - current_state[:, :2]  # [B, 2]
        
        print(f"\nStep {t} shapes:")
        print(f"  current_state: {current_state.shape}")
        print(f"  target_state: {target_state.shape}")
        print(f"  target_action: {target_action.shape}")
        
        # Test model call
        with torch.no_grad():
            probabilities, scores = choice_model(obs_seq, current_state, candidate_actions)
        
        print(f"  probabilities: {probabilities.shape}")
        print(f"  scores: {scores.shape}")
        
        # Test action matching
        action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)  # [B, N, 2]
        action_distances = torch.norm(action_diffs, dim=2)  # [B, N]
        target_action_idx = torch.argmin(action_distances, dim=1)  # [B]
        
        print(f"  action_diffs: {action_diffs.shape}")
        print(f"  action_distances: {action_distances.shape}")
        print(f"  target_action_idx: {target_action_idx.shape}")
        
        # Test loss computation
        batch_probs = probabilities[target_action_idx, torch.arange(batch_size)]
        loss = -torch.log(batch_probs + 1e-8).mean()
        
        print(f"  batch_probs: {batch_probs.shape}")
        print(f"  loss: {loss.item():.4f}")
        
        print(f"\n✅ Training dimensions test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error in training dimensions test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Model Dimension Fix Testing")
    print("=" * 50)
    
    tests = [
        test_model_forward,
        test_training_dimensions
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n📋 Test Summary")
    print("=" * 50)
    
    test_names = [
        "Model forward pass",
        "Training dimensions"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    if all(results):
        print("\n🎉 All tests passed! Model fix is working.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
