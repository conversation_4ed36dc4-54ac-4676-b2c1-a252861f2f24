"""
Comprehensive Visualization System for Trajectory Prediction

This module provides:
1. Trajectory visualization with risk fields
2. Action probability heatmaps
3. Multi-step rollout visualization
4. Interactive plots for analysis
5. Animation capabilities
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.animation as animation
from matplotlib.colors import LinearSegmentedColormap
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
import seaborn as sns
from dataclasses import dataclass


class TrajectoryVisualizer:
    """
    Main visualization class for trajectory prediction results.
    """

    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        self.figsize = figsize
        self.colors = {
            'pedestrian': '#FF6B6B',
            'vehicle': '#4ECDC4',
            'trajectory': '#45B7D1',
            'risk_high': '#FF4757',
            'risk_medium': '#FFA502',
            'risk_low': '#26de81',
            'action_selected': '#5f27cd',
            'action_candidate': '#a55eea'
        }

    def plot_single_step_prediction(self,
                                  state_history: torch.Tensor,
                                  current_state: torch.Tensor,
                                  predicted_states: torch.Tensor,
                                  probabilities: torch.Tensor,
                                  scores: torch.Tensor,
                                  candidate_actions: torch.Tensor,
                                  selected_action_idx: int,
                                  batch_idx: int = 0,
                                  save_path: Optional[str] = None):
        """
        Visualize single-step prediction with risk field and action probabilities.

        Args:
            state_history: (B, T, 8) - historical states
            current_state: (B, 8) - current state
            predicted_states: (N, B, 8) - predicted states for each action
            probabilities: (N, B) - action probabilities
            scores: (N, B) - risk scores
            candidate_actions: (N, 2) - candidate actions
            selected_action_idx: Selected action index
            batch_idx: Which batch element to visualize
            save_path: Optional path to save figure
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('Single-Step Trajectory Prediction Analysis', fontsize=16, fontweight='bold')

        # Extract data for visualization
        hist = state_history[batch_idx].cpu().numpy()  # (T, 8)
        curr = current_state[batch_idx].cpu().numpy()  # (8,)
        pred = predicted_states[:, batch_idx].cpu().numpy()  # (N, 8)
        probs = probabilities[:, batch_idx].cpu().numpy()  # (N,)
        risk_scores = scores[:, batch_idx].cpu().numpy()  # (N,)
        actions = candidate_actions.cpu().numpy()  # (N, 2)

        # 1. Trajectory and risk field visualization
        ax1 = axes[0, 0]
        self._plot_trajectory_with_risk(ax1, hist, curr, pred, probs, selected_action_idx)
        ax1.set_title('Trajectory Prediction with Risk Field')
        ax1.set_xlabel('X Position (m)')
        ax1.set_ylabel('Y Position (m)')

        # 2. Action probability heatmap
        ax2 = axes[0, 1]
        self._plot_action_probabilities(ax2, actions, probs, selected_action_idx)
        ax2.set_title('Action Probability Distribution')

        # 3. Risk score analysis
        ax3 = axes[1, 0]
        self._plot_risk_scores(ax3, actions, risk_scores, probs, selected_action_idx)
        ax3.set_title('Risk Scores vs Action Probabilities')

        # 4. State evolution
        ax4 = axes[1, 1]
        self._plot_state_evolution(ax4, hist, curr, pred[selected_action_idx])
        ax4.set_title('State Evolution (Selected Action)')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def _plot_trajectory_with_risk(self, ax, hist, curr, pred, probs, selected_idx):
        """Plot trajectory with risk field visualization."""
        # Historical trajectory
        ped_hist = hist[:, :2]  # (T, 2)
        ax.plot(ped_hist[:, 0], ped_hist[:, 1], 'o-',
                color=self.colors['pedestrian'], alpha=0.6, label='History')

        # Current position
        ax.plot(curr[0], curr[1], 'o', color=self.colors['pedestrian'],
                markersize=10, label='Current')

        # Vehicle position
        ax.plot(curr[4], curr[5], 's', color=self.colors['vehicle'],
                markersize=12, label='Vehicle')

        # Predicted positions with risk coloring
        pred_pos = pred[:, :2]  # (N, 2)

        # Create risk-based colormap
        risk_colors = plt.cm.Reds(1 - probs / probs.max())  # Higher prob = lower risk = lighter red

        scatter = ax.scatter(pred_pos[:, 0], pred_pos[:, 1],
                           c=probs, cmap='RdYlGn', s=60, alpha=0.7,
                           label='Predictions')

        # Highlight selected action
        ax.plot(pred_pos[selected_idx, 0], pred_pos[selected_idx, 1],
                'o', color=self.colors['action_selected'], markersize=12,
                markeredgecolor='black', markeredgewidth=2, label='Selected')

        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Action Probability')

        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')

    def _plot_action_probabilities(self, ax, actions, probs, selected_idx):
        """Plot action probabilities as heatmap."""
        # Reshape actions for heatmap
        speed_changes = actions[:, 0]
        heading_changes = actions[:, 1]

        # Create grid for heatmap
        speed_unique = np.unique(speed_changes)
        heading_unique = np.unique(heading_changes)

        prob_grid = np.zeros((len(speed_unique), len(heading_unique)))

        for i, speed in enumerate(speed_unique):
            for j, heading in enumerate(heading_unique):
                mask = (speed_changes == speed) & (heading_changes == heading)
                if mask.any():
                    prob_grid[i, j] = probs[mask][0]

        im = ax.imshow(prob_grid, cmap='viridis', aspect='auto', origin='lower')

        # Set ticks and labels
        ax.set_xticks(range(len(heading_unique)))
        ax.set_yticks(range(len(speed_unique)))
        ax.set_xticklabels([f'{h:.2f}' for h in heading_unique])
        ax.set_yticklabels([f'{s:.2f}' for s in speed_unique])

        ax.set_xlabel('Heading Change (rad)')
        ax.set_ylabel('Speed Change (m/s)')

        # Highlight selected action
        selected_action = actions[selected_idx]
        speed_idx = np.where(speed_unique == selected_action[0])[0][0]
        heading_idx = np.where(heading_unique == selected_action[1])[0][0]

        rect = patches.Rectangle((heading_idx-0.4, speed_idx-0.4), 0.8, 0.8,
                               linewidth=3, edgecolor='red', facecolor='none')
        ax.add_patch(rect)

        plt.colorbar(im, ax=ax, label='Probability')

    def _plot_risk_scores(self, ax, actions, risk_scores, probs, selected_idx):
        """Plot risk scores vs probabilities."""
        # Scatter plot of risk vs probability
        scatter = ax.scatter(risk_scores, probs, alpha=0.6, s=50)

        # Highlight selected action
        ax.scatter(risk_scores[selected_idx], probs[selected_idx],
                  color=self.colors['action_selected'], s=100,
                  edgecolor='black', linewidth=2, label='Selected')

        ax.set_xlabel('Risk Score')
        ax.set_ylabel('Action Probability')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Add trend line
        z = np.polyfit(risk_scores, probs, 1)
        p = np.poly1d(z)
        ax.plot(risk_scores, p(risk_scores), "r--", alpha=0.8, label='Trend')

    def _plot_state_evolution(self, ax, hist, curr, pred_next):
        """Plot state component evolution."""
        # Combine history, current, and prediction
        all_states = np.vstack([hist, curr.reshape(1, -1), pred_next.reshape(1, -1)])

        # Plot key state components
        time_steps = np.arange(len(all_states))

        # Pedestrian speed
        ped_speeds = np.linalg.norm(all_states[:, 2:4], axis=1)
        ax.plot(time_steps, ped_speeds, 'o-', label='Ped Speed', color=self.colors['pedestrian'])

        # Vehicle speed
        av_speeds = all_states[:, 6]
        ax.plot(time_steps, av_speeds, 's-', label='AV Speed', color=self.colors['vehicle'])

        # Mark current and prediction
        ax.axvline(len(hist), color='gray', linestyle='--', alpha=0.7, label='Current')
        ax.axvline(len(hist)+1, color='red', linestyle='--', alpha=0.7, label='Prediction')

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Speed (m/s)')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_multi_step_trajectory(self,
                                 trajectory_prediction,
                                 initial_state_history: torch.Tensor,
                                 ground_truth: Optional[torch.Tensor] = None,
                                 batch_idx: int = 0,
                                 save_path: Optional[str] = None):
        """
        Visualize multi-step trajectory prediction.

        Args:
            trajectory_prediction: TrajectoryPrediction object
            initial_state_history: (B, T, 8) - initial state history
            ground_truth: Optional (H, B, 8) - ground truth trajectory
            batch_idx: Which batch element to visualize
            save_path: Optional path to save figure
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        fig.suptitle('Multi-Step Trajectory Prediction', fontsize=16, fontweight='bold')

        # Extract data
        pred_states = trajectory_prediction.states[:, batch_idx].cpu().numpy()  # (H, 8)
        pred_actions = trajectory_prediction.actions[:, batch_idx].cpu().numpy()  # (H, 2)
        confidence = trajectory_prediction.confidence[:, batch_idx].cpu().numpy()  # (H,)
        hist = initial_state_history[batch_idx].cpu().numpy()  # (T, 8)

        if ground_truth is not None:
            gt_states = ground_truth[:, batch_idx].cpu().numpy()  # (H, 8)
        else:
            gt_states = None

        # 1. Trajectory comparison
        ax1 = axes[0, 0]
        self._plot_trajectory_comparison(ax1, hist, pred_states, gt_states)
        ax1.set_title('Trajectory Comparison')

        # 2. Action sequence
        ax2 = axes[0, 1]
        self._plot_action_sequence(ax2, pred_actions)
        ax2.set_title('Selected Action Sequence')

        # 3. Confidence evolution
        ax3 = axes[1, 0]
        self._plot_confidence_evolution(ax3, confidence)
        ax3.set_title('Prediction Confidence Over Time')

        # 4. Speed profiles
        ax4 = axes[1, 1]
        self._plot_speed_profiles(ax4, hist, pred_states, gt_states)
        ax4.set_title('Speed Profiles')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def _plot_trajectory_comparison(self, ax, hist, pred_states, gt_states):
        """Plot predicted vs ground truth trajectories."""
        # Historical trajectory
        ped_hist = hist[:, :2]
        ax.plot(ped_hist[:, 0], ped_hist[:, 1], 'o-',
                color='gray', alpha=0.6, label='History')

        # Predicted trajectory
        pred_pos = pred_states[:, :2]
        ax.plot(pred_pos[:, 0], pred_pos[:, 1], 'o-',
                color=self.colors['trajectory'], linewidth=2, label='Predicted')

        # Ground truth (if available)
        if gt_states is not None:
            gt_pos = gt_states[:, :2]
            ax.plot(gt_pos[:, 0], gt_pos[:, 1], 's-',
                    color='red', alpha=0.7, label='Ground Truth')

        # Vehicle trajectory
        av_hist = hist[:, 4:6]
        av_pred = pred_states[:, 4:6]
        ax.plot(av_hist[:, 0], av_hist[:, 1], '^-',
                color=self.colors['vehicle'], alpha=0.6, label='AV History')
        ax.plot(av_pred[:, 0], av_pred[:, 1], '^-',
                color=self.colors['vehicle'], linewidth=2, label='AV Predicted')

        # Mark start and end
        ax.plot(ped_hist[-1, 0], ped_hist[-1, 1], 'go', markersize=10, label='Start')
        ax.plot(pred_pos[-1, 0], pred_pos[-1, 1], 'ro', markersize=10, label='End')

        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')

    def _plot_action_sequence(self, ax, pred_actions):
        """Plot sequence of selected actions."""
        horizon = len(pred_actions)
        time_steps = np.arange(horizon)

        # Speed changes
        ax.plot(time_steps, pred_actions[:, 0], 'o-',
                color=self.colors['action_selected'], label='Speed Change')

        # Heading changes
        ax2 = ax.twinx()
        ax2.plot(time_steps, pred_actions[:, 1], 's-',
                 color=self.colors['action_candidate'], label='Heading Change')

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Speed Change (m/s)', color=self.colors['action_selected'])
        ax2.set_ylabel('Heading Change (rad)', color=self.colors['action_candidate'])

        # Combine legends
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        ax.grid(True, alpha=0.3)

    def _plot_confidence_evolution(self, ax, confidence):
        """Plot prediction confidence over time."""
        horizon = len(confidence)
        time_steps = np.arange(horizon)

        ax.plot(time_steps, confidence, 'o-', color=self.colors['trajectory'], linewidth=2)
        ax.fill_between(time_steps, confidence, alpha=0.3, color=self.colors['trajectory'])

        # Add confidence thresholds
        ax.axhline(0.8, color='green', linestyle='--', alpha=0.7, label='High Confidence')
        ax.axhline(0.5, color='orange', linestyle='--', alpha=0.7, label='Medium Confidence')
        ax.axhline(0.2, color='red', linestyle='--', alpha=0.7, label='Low Confidence')

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Prediction Confidence')
        ax.set_ylim(0, 1)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_speed_profiles(self, ax, hist, pred_states, gt_states):
        """Plot speed profiles over time."""
        # Historical speeds
        hist_ped_speed = np.linalg.norm(hist[:, 2:4], axis=1)
        hist_av_speed = hist[:, 6]

        # Predicted speeds
        pred_ped_speed = np.linalg.norm(pred_states[:, 2:4], axis=1)
        pred_av_speed = pred_states[:, 6]

        # Time axes
        hist_time = np.arange(-len(hist), 0)
        pred_time = np.arange(len(pred_states))

        # Plot historical
        ax.plot(hist_time, hist_ped_speed, 'o-', color='gray', alpha=0.6, label='Ped History')
        ax.plot(hist_time, hist_av_speed, 's-', color='lightblue', alpha=0.6, label='AV History')

        # Plot predicted
        ax.plot(pred_time, pred_ped_speed, 'o-',
                color=self.colors['pedestrian'], linewidth=2, label='Ped Predicted')
        ax.plot(pred_time, pred_av_speed, 's-',
                color=self.colors['vehicle'], linewidth=2, label='AV Predicted')

        # Ground truth (if available)
        if gt_states is not None:
            gt_ped_speed = np.linalg.norm(gt_states[:, 2:4], axis=1)
            gt_av_speed = gt_states[:, 6]
            ax.plot(pred_time, gt_ped_speed, 'x-', color='red', alpha=0.7, label='Ped GT')
            ax.plot(pred_time, gt_av_speed, 'x-', color='darkred', alpha=0.7, label='AV GT')

        # Mark present
        ax.axvline(0, color='black', linestyle='--', alpha=0.7, label='Present')

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Speed (m/s)')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_multiple_trajectories(self,
                                 trajectories: List,
                                 initial_state_history: torch.Tensor,
                                 batch_idx: int = 0,
                                 save_path: Optional[str] = None):
        """
        Visualize multiple trajectory samples (for stochastic predictions).

        Args:
            trajectories: List of TrajectoryPrediction objects
            initial_state_history: (B, T, 8) - initial state history
            batch_idx: Which batch element to visualize
            save_path: Optional path to save figure
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('Multiple Trajectory Samples', fontsize=16, fontweight='bold')

        hist = initial_state_history[batch_idx].cpu().numpy()

        # 1. Trajectory fan
        ax1 = axes[0]
        self._plot_trajectory_fan(ax1, hist, trajectories, batch_idx)
        ax1.set_title('Trajectory Uncertainty')

        # 2. Confidence distribution
        ax2 = axes[1]
        self._plot_confidence_distribution(ax2, trajectories, batch_idx)
        ax2.set_title('Confidence Distribution')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def _plot_trajectory_fan(self, ax, hist, trajectories, batch_idx):
        """Plot multiple trajectories as a fan."""
        # Historical trajectory
        ped_hist = hist[:, :2]
        ax.plot(ped_hist[:, 0], ped_hist[:, 1], 'o-',
                color='gray', alpha=0.8, linewidth=2, label='History')

        # Plot all trajectory samples
        all_positions = []
        for i, traj in enumerate(trajectories):
            pred_states = traj.states[:, batch_idx].cpu().numpy()
            pred_pos = pred_states[:, :2]
            all_positions.append(pred_pos)

            alpha = 0.3 if len(trajectories) > 5 else 0.6
            ax.plot(pred_pos[:, 0], pred_pos[:, 1], '-',
                    color=self.colors['trajectory'], alpha=alpha, linewidth=1)

        # Compute and plot confidence ellipses
        all_positions = np.array(all_positions)  # (num_samples, horizon, 2)

        for step in range(0, all_positions.shape[1], 2):  # Every 2nd step
            positions_at_step = all_positions[:, step, :]  # (num_samples, 2)

            if len(positions_at_step) > 2:
                # Compute 95% confidence ellipse
                mean_pos = np.mean(positions_at_step, axis=0)
                cov = np.cov(positions_at_step.T)

                # Eigenvalues and eigenvectors for ellipse
                eigenvals, eigenvecs = np.linalg.eigh(cov)
                angle = np.degrees(np.arctan2(eigenvecs[1, 0], eigenvecs[0, 0]))

                # 95% confidence interval (chi-square with 2 DOF)
                scale = 2.448  # sqrt(5.991) for 95% confidence
                width, height = 2 * scale * np.sqrt(eigenvals)

                ellipse = patches.Ellipse(mean_pos, width, height, angle=angle,
                                        facecolor='none', edgecolor=self.colors['trajectory'],
                                        alpha=0.5, linestyle='--')
                ax.add_patch(ellipse)

        # Mark start
        ax.plot(ped_hist[-1, 0], ped_hist[-1, 1], 'go', markersize=10, label='Start')

        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')

    def _plot_confidence_distribution(self, ax, trajectories, batch_idx):
        """Plot confidence distribution across samples."""
        horizon = trajectories[0].states.shape[0]
        time_steps = np.arange(horizon)

        # Collect confidence values
        all_confidences = []
        for traj in trajectories:
            confidence = traj.confidence[:, batch_idx].cpu().numpy()
            all_confidences.append(confidence)
            ax.plot(time_steps, confidence, '-', alpha=0.3, color=self.colors['trajectory'])

        all_confidences = np.array(all_confidences)  # (num_samples, horizon)

        # Plot statistics
        mean_conf = np.mean(all_confidences, axis=0)
        std_conf = np.std(all_confidences, axis=0)

        ax.plot(time_steps, mean_conf, 'o-', color='red', linewidth=2, label='Mean')
        ax.fill_between(time_steps, mean_conf - std_conf, mean_conf + std_conf,
                       alpha=0.3, color='red', label='±1 Std')

        ax.set_xlabel('Time Step')
        ax.set_ylabel('Prediction Confidence')
        ax.set_ylim(0, 1)
        ax.legend()
        ax.grid(True, alpha=0.3)

    def create_trajectory_animation(self,
                                  trajectory_prediction,
                                  initial_state_history: torch.Tensor,
                                  batch_idx: int = 0,
                                  save_path: Optional[str] = None,
                                  interval: int = 500):
        """
        Create animated visualization of trajectory prediction.

        Args:
            trajectory_prediction: TrajectoryPrediction object
            initial_state_history: (B, T, 8) - initial state history
            batch_idx: Which batch element to visualize
            save_path: Optional path to save animation (as gif)
            interval: Animation interval in milliseconds
        """
        # Extract data
        pred_states = trajectory_prediction.states[:, batch_idx].cpu().numpy()
        hist = initial_state_history[batch_idx].cpu().numpy()

        # Setup figure
        fig, ax = plt.subplots(figsize=(10, 8))
        ax.set_title('Trajectory Prediction Animation')
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.grid(True, alpha=0.3)

        # Plot historical trajectory
        ped_hist = hist[:, :2]
        ax.plot(ped_hist[:, 0], ped_hist[:, 1], 'o-',
                color='gray', alpha=0.6, label='History')

        # Initialize animated elements
        ped_point, = ax.plot([], [], 'o', color=self.colors['pedestrian'],
                            markersize=10, label='Pedestrian')
        av_point, = ax.plot([], [], 's', color=self.colors['vehicle'],
                           markersize=12, label='Vehicle')
        pred_line, = ax.plot([], [], '--', color=self.colors['trajectory'],
                            alpha=0.7, label='Future Prediction')

        # Set axis limits
        all_pos = np.vstack([ped_hist, pred_states[:, :2], pred_states[:, 4:6]])
        margin = 2.0
        ax.set_xlim(all_pos[:, 0].min() - margin, all_pos[:, 0].max() + margin)
        ax.set_ylim(all_pos[:, 1].min() - margin, all_pos[:, 1].max() + margin)
        ax.set_aspect('equal')
        ax.legend()

        def animate(frame):
            if frame < len(pred_states):
                # Current positions
                ped_pos = pred_states[frame, :2]
                av_pos = pred_states[frame, 4:6]

                # Update points
                ped_point.set_data([ped_pos[0]], [ped_pos[1]])
                av_point.set_data([av_pos[0]], [av_pos[1]])

                # Update prediction line (remaining trajectory)
                if frame < len(pred_states) - 1:
                    future_pos = pred_states[frame+1:, :2]
                    pred_line.set_data(future_pos[:, 0], future_pos[:, 1])
                else:
                    pred_line.set_data([], [])

                ax.set_title(f'Trajectory Prediction Animation (Step {frame+1}/{len(pred_states)})')

            return ped_point, av_point, pred_line

        # Create animation
        anim = animation.FuncAnimation(fig, animate, frames=len(pred_states),
                                     interval=interval, blit=True, repeat=True)

        if save_path:
            anim.save(save_path, writer='pillow', fps=1000//interval)

        plt.show()
        return anim


def create_comprehensive_report(inference_system,
                              state_history: torch.Tensor,
                              current_state: torch.Tensor,
                              ground_truth: Optional[torch.Tensor] = None,
                              batch_idx: int = 0,
                              save_dir: Optional[str] = None):
    """
    Create a comprehensive visualization report.

    Args:
        inference_system: TrajectoryInference instance
        state_history: (B, T, 8) - state history
        current_state: (B, 8) - current state
        ground_truth: Optional (H, B, 8) - ground truth trajectory
        batch_idx: Which batch element to visualize
        save_dir: Optional directory to save all plots
    """
    visualizer = TrajectoryVisualizer()

    # Get candidate actions
    candidate_actions = inference_system.action_sampler.get_candidates(current_state.device)

    # Single-step prediction
    probabilities, scores = inference_system.choice_model(
        state_history, current_state, candidate_actions
    )

    # Predict states for visualization
    predicted_states, _ = inference_system.choice_model.trajectory_predictor(
        state_history, candidate_actions, current_state
    )

    selected_action_idx = torch.argmax(probabilities[:, batch_idx]).item()

    # 1. Single-step analysis
    save_path = f"{save_dir}/single_step_analysis.png" if save_dir else None
    visualizer.plot_single_step_prediction(
        state_history, current_state, predicted_states, probabilities, scores,
        candidate_actions, selected_action_idx, batch_idx, save_path
    )

    # 2. Multi-step trajectory
    trajectory = inference_system.predict_trajectory(state_history, current_state)
    save_path = f"{save_dir}/multi_step_trajectory.png" if save_dir else None
    visualizer.plot_multi_step_trajectory(
        trajectory, state_history, ground_truth, batch_idx, save_path
    )

    # 3. Multiple samples (if stochastic)
    if inference_system.selection_mode == 'stochastic':
        trajectories = inference_system.predict_multiple_trajectories(
            state_history, current_state, num_samples=10
        )
        save_path = f"{save_dir}/multiple_trajectories.png" if save_dir else None
        visualizer.plot_multiple_trajectories(
            trajectories, state_history, batch_idx, save_path
        )

    # 4. Animation
    save_path = f"{save_dir}/trajectory_animation.gif" if save_dir else None
    anim = visualizer.create_trajectory_animation(
        trajectory, state_history, batch_idx, save_path
    )

    print("Comprehensive visualization report completed!")
    return visualizer, trajectory


if __name__ == "__main__":
    # Test visualization system
    print("Testing trajectory visualization system...")

    # This would normally import the actual models
    # For testing, we'll create dummy data

    # Create dummy trajectory prediction
    from trajectory_inference import TrajectoryPrediction

    horizon = 10
    batch_size = 2
    state_dim = 8

    dummy_states = torch.randn(horizon, batch_size, state_dim)
    dummy_actions = torch.randn(horizon, batch_size, 2)
    dummy_probs = torch.rand(horizon, 25, batch_size)  # 25 candidate actions
    dummy_scores = torch.randn(horizon, 25, batch_size)
    dummy_confidence = torch.rand(horizon, batch_size)

    trajectory = TrajectoryPrediction(
        states=dummy_states,
        actions=dummy_actions,
        probabilities=dummy_probs,
        scores=dummy_scores,
        confidence=dummy_confidence
    )

    initial_history = torch.randn(batch_size, 10, state_dim)

    # Test visualizer
    visualizer = TrajectoryVisualizer()

    print("Creating multi-step trajectory visualization...")
    visualizer.plot_multi_step_trajectory(trajectory, initial_history)

    print("Visualization system test completed!")