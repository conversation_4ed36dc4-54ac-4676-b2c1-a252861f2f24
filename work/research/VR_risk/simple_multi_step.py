"""
Simplified multi-step trajectory generator for Option 1.
This version is more robust and easier to use.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass

from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel


@dataclass
class SimpleTrajectory:
    """A simple trajectory with risk information."""
    positions: np.ndarray      # [horizon, 2] - pedestrian positions
    states: np.ndarray         # [horizon, state_dim] - full states
    actions: np.ndarray        # [horizon, 2] - actions taken
    total_risk: float          # Total risk for this trajectory
    step_risks: np.ndarray     # [horizon] - risk at each step


class SimpleMultiStepPredictor:
    """
    Simplified multi-step trajectory predictor for Option 1.
    Uses pre-trained models without additional training.
    """
    
    def __init__(self, 
                 trajectory_predictor: LSTMTrajectoryPredictor,
                 risk_model: EnhancedRiskModel,
                 action_sampler: ActionSampler,
                 horizon: int = 10,
                 num_candidates: int = 10):
        """
        Initialize simple multi-step predictor.
        
        Args:
            trajectory_predictor: Pre-trained LSTM predictor
            risk_model: Pre-trained risk model
            action_sampler: Action sampler
            horizon: Number of steps to predict
            num_candidates: Number of trajectory candidates
        """
        self.trajectory_predictor = trajectory_predictor
        self.risk_model = risk_model
        self.action_sampler = action_sampler
        self.horizon = horizon
        self.num_candidates = num_candidates
        
        # Create choice model
        self.choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        print(f"✅ Simple multi-step predictor initialized")
        print(f"  Horizon: {horizon} steps")
        print(f"  Candidates: {num_candidates}")
    
    def generate_single_trajectory(self, 
                                 state_history: torch.Tensor,
                                 current_state: torch.Tensor,
                                 use_greedy: bool = False) -> SimpleTrajectory:
        """
        Generate a single trajectory.
        
        Args:
            state_history: [1, T, state_dim] - historical states
            current_state: [1, state_dim] - current state
            use_greedy: Whether to use greedy action selection
            
        Returns:
            SimpleTrajectory object
        """
        device = current_state.device
        candidate_actions = self.action_sampler.get_candidates(device)
        
        # Initialize trajectory storage
        trajectory_states = []
        trajectory_actions = []
        trajectory_risks = []
        
        # Start from current state
        step_state = current_state.clone()
        step_history = state_history.clone()
        
        for step in range(self.horizon):
            # Get action probabilities
            with torch.no_grad():
                probabilities, scores = self.choice_model(
                    step_history, step_state, candidate_actions
                )
            
            # Select action
            if use_greedy:
                action_idx = torch.argmax(probabilities, dim=0).item()
            else:
                # Sample from probabilities
                action_idx = torch.multinomial(probabilities[:, 0], 1).item()
            
            selected_action = candidate_actions[action_idx]  # [2]
            
            # Predict next state
            next_state = step_state.clone()
            next_state[0, :2] += selected_action  # Update pedestrian position
            
            # Simple risk computation
            ped_pos = next_state[0, :2]
            av_pos = next_state[0, 4:6]
            distance = torch.norm(ped_pos - av_pos)
            step_risk = torch.exp(-distance / 5.0).item()
            
            # Store step information
            trajectory_states.append(next_state[0].cpu().numpy())
            trajectory_actions.append(selected_action.cpu().numpy())
            trajectory_risks.append(step_risk)
            
            # Update for next step
            step_state = next_state
            # Update history by appending new state
            step_history = torch.cat([step_history[:, 1:], next_state.unsqueeze(1)], dim=1)
        
        # Create trajectory object
        states = np.array(trajectory_states)  # [horizon, state_dim]
        actions = np.array(trajectory_actions)  # [horizon, 2]
        risks = np.array(trajectory_risks)  # [horizon]
        positions = states[:, :2]  # [horizon, 2]
        total_risk = np.sum(risks)
        
        return SimpleTrajectory(
            positions=positions,
            states=states,
            actions=actions,
            total_risk=total_risk,
            step_risks=risks
        )
    
    def predict_trajectories(self, 
                           state_history: torch.Tensor,
                           current_state: torch.Tensor) -> Dict:
        """
        Predict multiple trajectories and select the best one.
        
        Args:
            state_history: [B, T, state_dim] - historical states
            current_state: [B, state_dim] - current state
            
        Returns:
            Dictionary with trajectory predictions and analysis
        """
        batch_size = current_state.shape[0]
        results = []
        
        for b in range(batch_size):
            # Extract single batch item
            single_history = state_history[b:b+1]  # [1, T, state_dim]
            single_current = current_state[b:b+1]  # [1, state_dim]
            
            # Generate multiple trajectory candidates
            candidates = []
            for i in range(self.num_candidates):
                # Use greedy for first candidate, sampling for others
                use_greedy = (i == 0)
                trajectory = self.generate_single_trajectory(
                    single_history, single_current, use_greedy=use_greedy
                )
                candidates.append(trajectory)
            
            # Select best trajectory (minimum risk)
            best_trajectory = min(candidates, key=lambda t: t.total_risk)
            
            # Analyze results
            all_risks = [t.total_risk for t in candidates]
            
            result = {
                'best_trajectory': {
                    'positions': best_trajectory.positions,
                    'states': best_trajectory.states,
                    'actions': best_trajectory.actions,
                    'total_risk': best_trajectory.total_risk,
                    'step_risks': best_trajectory.step_risks
                },
                'risk_analysis': {
                    'min_risk': min(all_risks),
                    'max_risk': max(all_risks),
                    'mean_risk': np.mean(all_risks),
                    'std_risk': np.std(all_risks),
                    'all_risks': all_risks
                },
                'trajectory_diversity': {
                    'num_candidates': len(candidates),
                    'risk_range': max(all_risks) - min(all_risks)
                }
            }
            results.append(result)
        
        return results if batch_size > 1 else results[0]
    
    def visualize_trajectory(self, result: Dict, save_path: Optional[str] = None):
        """
        Simple visualization of trajectory result.
        
        Args:
            result: Result from predict_trajectories
            save_path: Optional path to save plot
        """
        import matplotlib.pyplot as plt
        
        best_traj = result['best_trajectory']
        positions = best_traj['positions']
        step_risks = best_traj['step_risks']
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 1. Trajectory with risk coloring
        ax = axes[0]
        scatter = ax.scatter(positions[:, 0], positions[:, 1], 
                           c=step_risks, cmap='Reds', s=100, alpha=0.8)
        ax.plot(positions[:, 0], positions[:, 1], 'k-', alpha=0.5, linewidth=1)
        
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Risk Score')
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title('Predicted Trajectory')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Risk over time
        ax = axes[1]
        ax.plot(range(len(step_risks)), step_risks, 'r-', linewidth=2, marker='o')
        ax.fill_between(range(len(step_risks)), step_risks, alpha=0.3, color='red')
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Risk Score')
        ax.set_title('Risk Evolution')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
        
        plt.show()
        
        # Print summary
        print(f"📊 Trajectory Summary:")
        print(f"  Total risk: {best_traj['total_risk']:.4f}")
        print(f"  Risk range: {result['trajectory_diversity']['risk_range']:.4f}")
        print(f"  Candidates evaluated: {result['trajectory_diversity']['num_candidates']}")


def test_simple_multistep():
    """Test the simple multi-step predictor."""
    print("🧪 Testing Simple Multi-Step Predictor")
    print("=" * 50)
    
    device = torch.device('cpu')
    
    # Initialize models
    trajectory_predictor = LSTMTrajectoryPredictor().to(device)
    risk_model = EnhancedRiskModel().to(device)
    action_sampler = ActionSampler()
    
    # Create simple predictor
    predictor = SimpleMultiStepPredictor(
        trajectory_predictor=trajectory_predictor,
        risk_model=risk_model,
        action_sampler=action_sampler,
        horizon=10,
        num_candidates=8
    )
    
    # Create test data
    batch_size = 1
    seq_len = 10
    state_dim = 8
    
    state_history = torch.randn(batch_size, seq_len, state_dim)
    current_state = torch.randn(batch_size, state_dim)
    
    # Set realistic positions
    current_state[0, 0] = 5.0   # Ped X
    current_state[0, 1] = 2.0   # Ped Y
    current_state[0, 4] = 20.0  # AV X
    current_state[0, 5] = 2.5   # AV Y
    
    # Test prediction
    print("\n🎯 Generating trajectories...")
    result = predictor.predict_trajectories(state_history, current_state)
    
    print("✅ Prediction successful!")
    print(f"  Best trajectory risk: {result['best_trajectory']['total_risk']:.4f}")
    print(f"  Risk statistics: min={result['risk_analysis']['min_risk']:.4f}, "
          f"max={result['risk_analysis']['max_risk']:.4f}")
    
    # Test visualization
    print("\n📊 Creating visualization...")
    predictor.visualize_trajectory(result, save_path='simple_multistep_test.png')
    
    print("✅ Simple multi-step predictor test successful!")
    return True


if __name__ == "__main__":
    test_simple_multistep()
