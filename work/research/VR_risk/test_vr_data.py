#!/usr/bin/env python3
"""
Test script for VR data loading and basic functionality.
"""

import torch
import numpy as np
from vr_data_loader import create_vr_dataloaders, get_dataset_info
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel

def test_vr_data_loading():
    """Test VR data loading functionality."""
    print("🧪 Testing VR Data Loading")
    print("=" * 40)
    
    try:
        # Test dataset info
        data_path = 'processed_data'
        dataset_info = get_dataset_info(data_path)
        
        print(f"✅ Dataset info loaded successfully")
        print(f"   Total samples: {dataset_info['total_samples']:,}")
        print(f"   Subjects: {dataset_info['num_subjects']}")
        print(f"   Trials: {dataset_info['num_trials']}")
        print(f"   Crossings: {dataset_info['crossings']:,}")
        print(f"   Non-crossings: {dataset_info['non_crossings']:,}")
        
        # Test data loaders
        train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
            data_path=data_path,
            batch_size=8,  # Small batch for testing
            obs_len=10,
            pred_len=10,
            train_split=0.7,
            val_split=0.15,
            normalize=True,
            num_workers=0  # No multiprocessing for testing
        )
        
        print(f"✅ Data loaders created successfully")
        print(f"   Train batches: {len(train_loader)}")
        print(f"   Val batches: {len(val_loader)}")
        print(f"   Test batches: {len(test_loader)}")
        
        # Test batch loading
        sample_batch = next(iter(train_loader))
        obs_seq, pred_seq, metadata = sample_batch
        
        print(f"✅ Sample batch loaded successfully")
        print(f"   Obs shape: {obs_seq.shape}")
        print(f"   Pred shape: {pred_seq.shape}")
        print(f"   Metadata count: {len(metadata)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in VR data loading: {e}")
        return False

def test_model_initialization():
    """Test model initialization."""
    print("\n🧪 Testing Model Initialization")
    print("=" * 40)
    
    try:
        device = torch.device('cpu')  # Use CPU for testing
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        print(f"✅ Models initialized successfully")
        print(f"   Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}")
        print(f"   Candidate actions: {action_sampler.num_actions}")
        
        # Test forward pass with dummy data
        batch_size = 4
        obs_len = 10
        state_dim = 8
        
        obs_seq = torch.randn(batch_size, obs_len, state_dim)
        current_state = torch.randn(batch_size, 1, state_dim)
        candidate_actions = action_sampler.get_candidates(device)
        
        with torch.no_grad():
            probabilities, scores = choice_model(obs_seq, current_state, candidate_actions)
        
        print(f"✅ Forward pass successful")
        print(f"   Probabilities shape: {probabilities.shape}")
        print(f"   Scores shape: {scores.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in model initialization: {e}")
        return False

def test_risk_parameters():
    """Test risk parameter display."""
    print("\n🧪 Testing Risk Parameter Display")
    print("=" * 40)
    
    try:
        risk_model = EnhancedRiskModel()
        
        print("Risk Component Weights (p parameters):")
        print(f"  Collision weight:     {risk_model.w_collision.item():.4f}")
        print(f"  Path deviation weight: {risk_model.w_path_dev.item():.4f}")
        print(f"  Behavioral weight:    {risk_model.w_behavioral.item():.4f}")
        print(f"  Perceptual weight:    {risk_model.w_perceptual.item():.4f}")
        
        print("\nAction Cost Weights (q parameters):")
        print(f"  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}")
        print(f"  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}")
        
        print("\nRisk Function Parameters:")
        print(f"  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}")
        print(f"  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}")
        print(f"  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}")
        print(f"  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}")
        
        print("✅ Risk parameters displayed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error in risk parameter display: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 VR Data and Model Testing")
    print("=" * 50)
    
    tests = [
        test_vr_data_loading,
        test_model_initialization,
        test_risk_parameters
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n📋 Test Summary")
    print("=" * 50)
    
    if all(results):
        print("🎉 All tests passed! System is ready for training.")
        print("\nNext steps:")
        print("1. Run 'jupyter notebook vr_training_demo.ipynb' for full training")
        print("2. Or run 'jupyter notebook trajectory_prediction_demo.ipynb' for system demo")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        failed_tests = [i for i, result in enumerate(results) if not result]
        print(f"Failed tests: {failed_tests}")

if __name__ == "__main__":
    main()
