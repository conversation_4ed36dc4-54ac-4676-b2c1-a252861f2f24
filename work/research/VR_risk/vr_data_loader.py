"""
VR Data Loader for LSTM Trajectory Prediction
Loads and preprocesses VR pedestrian crossing data for training and evaluation.
"""

import torch
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
import ast
import os
from typing import List, Dict, Tuple, Optional
import glob
from sklearn.model_selection import train_test_split


class VRTrajectoryDataset(Dataset):
    """
    Dataset class for VR pedestrian crossing data.
    
    State vector format: [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]
    """
    
    def __init__(self, data_path: str, obs_len: int = 10, pred_len: int = 10, 
                 normalize: bool = True, min_sequence_length: int = 25):
        """
        Initialize VR trajectory dataset.
        
        Args:
            data_path: Path to processed CSV files
            obs_len: Length of observation sequence
            pred_len: Length of prediction sequence  
            normalize: Whether to normalize the data
            min_sequence_length: Minimum sequence length to include
        """
        self.data_path = data_path
        self.obs_len = obs_len
        self.pred_len = pred_len
        self.seq_len = obs_len + pred_len
        self.normalize = normalize
        self.min_sequence_length = min_sequence_length
        
        # Load and process all data
        self.samples = []
        self.metadata = []
        self.stats = {}
        
        self._load_data()
        if self.normalize:
            self._compute_normalization_stats()
            self._normalize_data()
    
    def _load_data(self):
        """Load all CSV files and extract trajectory sequences."""
        csv_files = glob.glob(os.path.join(self.data_path, "*.csv"))
        print(f"Found {len(csv_files)} CSV files")
        
        total_sequences = 0
        valid_sequences = 0
        
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                sequences = self._process_file(df, csv_file)
                total_sequences += len(sequences)
                valid_sequences += len([s for s in sequences if len(s['states']) >= self.seq_len])
                self.samples.extend(sequences)
            except Exception as e:
                print(f"Error processing {csv_file}: {e}")
                continue
        
        print(f"Loaded {total_sequences} total sequences, {valid_sequences} valid sequences")
        
        # Filter sequences by minimum length and create sliding windows
        self._create_sliding_windows()
    
    def _process_file(self, df: pd.DataFrame, filename: str) -> List[Dict]:
        """Process a single CSV file into trajectory sequences."""
        sequences = []
        
        # Group by trial to get individual crossing attempts
        if 'trial_id' in df.columns:
            groups = df.groupby(['subject_id', 'trial_id'])
        else:
            # If no trial_id, treat each file as one sequence
            groups = [(('unknown', 'unknown'), df)]
        
        for (subject_id, trial_id), group in groups:
            if len(group) < self.min_sequence_length:
                continue
                
            states = self._extract_states(group)
            if len(states) < self.min_sequence_length:
                continue
            
            # Extract metadata
            metadata = {
                'subject_id': subject_id,
                'trial_id': trial_id,
                'filename': os.path.basename(filename),
                'CIT': group['CIT'].iloc[0] if 'CIT' in group.columns else 0.0,
                'certainty': group['certainty'].iloc[0] if 'certainty' in group.columns else 0.0,
                'crossing': group['crossing'].iloc[0] if 'crossing' in group.columns else 0,
                'brake_behavior': group['AV_Brake_Behaviour'].iloc[0] if 'AV_Brake_Behaviour' in group.columns else 'UNKNOWN',
                'timestamps': group['timestamp'].values
            }
            
            sequences.append({
                'states': states,
                'metadata': metadata
            })
        
        return sequences
    
    def _extract_states(self, group: pd.DataFrame) -> np.ndarray:
        """Extract state vectors from a trajectory group."""
        states = []
        
        for _, row in group.iterrows():
            try:
                # Parse pedestrian position
                if isinstance(row['ped_position'], str):
                    ped_pos = np.array(ast.literal_eval(row['ped_position']), dtype=np.float32)
                else:
                    ped_pos = np.array([row['ped_position'], 0.0], dtype=np.float32)
                
                # Parse AV position  
                if isinstance(row['AV_pos'], str):
                    av_pos = np.array(ast.literal_eval(row['AV_pos']), dtype=np.float32)
                else:
                    av_pos = np.array([row['AV_pos'], 0.0], dtype=np.float32)
                
                # Get speeds
                ped_speed = float(row['ped_speed']) if pd.notna(row['ped_speed']) else 0.0
                av_speed = float(row['AV_speed']) if pd.notna(row['AV_speed']) else 0.0
                
                states.append([ped_pos[0], ped_pos[1], ped_speed, 0.0,  # ped_x, ped_y, ped_vx, ped_vy
                              av_pos[0], av_pos[1], av_speed, 0.0])      # AV_x, AV_y, AV_v, AV_a
                
            except Exception as e:
                print(f"Error parsing row: {e}")
                continue
        
        if not states:
            return np.array([])
        
        states = np.array(states, dtype=np.float32)
        
        # Compute velocities from position differences
        if len(states) > 1:
            dt = 0.05  # Assume 20Hz sampling rate
            
            # Pedestrian velocities
            ped_vel = np.diff(states[:, :2], axis=0) / dt
            states[1:, 2:4] = ped_vel
            
            # AV acceleration
            av_accel = np.diff(states[:, 6]) / dt
            states[1:, 7] = av_accel
        
        return states

    def _create_sliding_windows(self):
        """Create sliding window samples from trajectory sequences."""
        windowed_samples = []
        windowed_metadata = []

        for seq_data in self.samples:
            states = seq_data['states']
            metadata = seq_data['metadata']

            if len(states) < self.seq_len:
                continue

            # Create sliding windows
            for i in range(len(states) - self.seq_len + 1):
                obs_seq = states[i:i + self.obs_len]
                pred_seq = states[i + self.obs_len:i + self.seq_len]

                windowed_samples.append({
                    'obs': obs_seq,
                    'pred': pred_seq
                })
                windowed_metadata.append(metadata)

        self.samples = windowed_samples
        self.metadata = windowed_metadata
        print(f"Created {len(self.samples)} sliding window samples")

    def _compute_normalization_stats(self):
        """Compute normalization statistics for the dataset."""
        all_states = []
        for sample in self.samples:
            all_states.append(sample['obs'])
            all_states.append(sample['pred'])

        all_states = np.concatenate(all_states, axis=0)

        self.stats = {
            'mean': np.mean(all_states, axis=0),
            'std': np.std(all_states, axis=0) + 1e-8  # Add small epsilon to avoid division by zero
        }

        print(f"Computed normalization stats:")
        print(f"  Mean: {self.stats['mean']}")
        print(f"  Std: {self.stats['std']}")

    def _normalize_data(self):
        """Normalize the data using computed statistics."""
        for sample in self.samples:
            sample['obs'] = (sample['obs'] - self.stats['mean']) / self.stats['std']
            sample['pred'] = (sample['pred'] - self.stats['mean']) / self.stats['std']

    def denormalize(self, data: np.ndarray) -> np.ndarray:
        """Denormalize data back to original scale."""
        if not self.normalize:
            return data
        return data * self.stats['std'] + self.stats['mean']

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]
        metadata = self.metadata[idx]

        obs = torch.tensor(sample['obs'], dtype=torch.float32)
        pred = torch.tensor(sample['pred'], dtype=torch.float32)

        return obs, pred, metadata


def create_vr_dataloaders(data_path: str, batch_size: int = 32, obs_len: int = 10,
                         pred_len: int = 10, train_split: float = 0.8,
                         val_split: float = 0.1, normalize: bool = True,
                         num_workers: int = 4) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test dataloaders for VR data.

    Args:
        data_path: Path to processed CSV files
        batch_size: Batch size for dataloaders
        obs_len: Length of observation sequence
        pred_len: Length of prediction sequence
        train_split: Fraction of data for training
        val_split: Fraction of data for validation
        normalize: Whether to normalize the data
        num_workers: Number of workers for data loading

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Load full dataset
    full_dataset = VRTrajectoryDataset(
        data_path=data_path,
        obs_len=obs_len,
        pred_len=pred_len,
        normalize=normalize
    )

    # Split dataset
    total_size = len(full_dataset)
    train_size = int(train_split * total_size)
    val_size = int(val_split * total_size)
    test_size = total_size - train_size - val_size

    train_dataset, temp_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size + test_size]
    )
    val_dataset, test_dataset = torch.utils.data.random_split(
        temp_dataset, [val_size, test_size]
    )

    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        num_workers=num_workers, pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=num_workers, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=num_workers, pin_memory=True
    )

    print(f"Dataset splits:")
    print(f"  Train: {len(train_dataset)} samples")
    print(f"  Val: {len(val_dataset)} samples")
    print(f"  Test: {len(test_dataset)} samples")

    return train_loader, val_loader, test_loader, full_dataset.stats


def get_dataset_info(data_path: str) -> Dict:
    """Get information about the VR dataset."""
    dataset = VRTrajectoryDataset(data_path, normalize=False)

    # Collect statistics
    subjects = set()
    trials = set()
    crossings = 0
    non_crossings = 0
    brake_behaviors = {}

    for metadata in dataset.metadata:
        subjects.add(metadata['subject_id'])
        trials.add((metadata['subject_id'], metadata['trial_id']))

        if metadata['crossing'] == 1:
            crossings += 1
        else:
            non_crossings += 1

        brake_behavior = metadata['brake_behavior']
        brake_behaviors[brake_behavior] = brake_behaviors.get(brake_behavior, 0) + 1

    return {
        'total_samples': len(dataset),
        'num_subjects': len(subjects),
        'num_trials': len(trials),
        'crossings': crossings,
        'non_crossings': non_crossings,
        'brake_behaviors': brake_behaviors,
        'normalization_stats': dataset.stats if dataset.normalize else None
    }
