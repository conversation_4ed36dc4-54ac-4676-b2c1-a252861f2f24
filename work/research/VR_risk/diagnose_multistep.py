#!/usr/bin/env python3
"""
Diagnostic script to understand why multi-step prediction is failing.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt

def diagnose_multistep_issues():
    """Diagnose issues with multi-step prediction."""
    print("🔍 DIAGNOSING MULTI-STEP PREDICTION ISSUES")
    print("=" * 60)
    
    device = torch.device('cpu')  # Use CPU for debugging
    
    try:
        # Import modules
        from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
        from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel
        from vr_data_loader import create_vr_dataloaders
        from simple_multi_step import SimpleMultiStepPredictor
        
        # Load a small amount of data
        print("📊 Loading test data...")
        train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
            data_path='processed_data',
            batch_size=4,
            obs_len=10,
            pred_len=10,
            normalize=True,
            num_workers=0
        )
        
        # Get one test sample
        obs_seq, pred_seq, metadata = next(iter(test_loader))
        sample_obs = obs_seq[0:1]  # [1, 10, 8]
        sample_pred = pred_seq[0:1]  # [1, 10, 8]
        current_state = obs_seq[0, -1].unsqueeze(0)  # [1, 8]
        
        print(f"✅ Data loaded")
        print(f"  Obs shape: {sample_obs.shape}")
        print(f"  Pred shape: {sample_pred.shape}")
        print(f"  Current state shape: {current_state.shape}")
        
        # Check data ranges
        print(f"\n📊 DATA ANALYSIS:")
        print(f"  Obs range: [{sample_obs.min():.3f}, {sample_obs.max():.3f}]")
        print(f"  Current state: {current_state[0].numpy()}")
        print(f"  Ground truth final: {sample_pred[0, -1].numpy()}")
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        # Test single-step prediction first
        print(f"\n🧪 TESTING SINGLE-STEP PREDICTION:")
        candidate_actions = action_sampler.get_candidates(device)
        print(f"  Candidate actions shape: {candidate_actions.shape}")
        print(f"  Action range: [{candidate_actions.min():.3f}, {candidate_actions.max():.3f}]")
        
        with torch.no_grad():
            probabilities, scores = choice_model(sample_obs, current_state, candidate_actions)
        
        print(f"  Probabilities shape: {probabilities.shape}")
        print(f"  Probabilities range: [{probabilities.min():.6f}, {probabilities.max():.6f}]")
        print(f"  Probabilities sum: {probabilities.sum(dim=0)}")
        
        # Check if probabilities are reasonable
        best_action_idx = torch.argmax(probabilities, dim=0).item()
        best_action = candidate_actions[best_action_idx]
        print(f"  Best action: {best_action.numpy()}")
        print(f"  Best probability: {probabilities[best_action_idx, 0].item():.6f}")
        
        # Test what happens when we apply the action
        next_state = current_state.clone()
        next_state[0, :2] += best_action
        print(f"  Current ped pos: {current_state[0, :2].numpy()}")
        print(f"  Next ped pos: {next_state[0, :2].numpy()}")
        print(f"  Position change: {best_action.numpy()}")
        
        # Test multi-step prediction
        print(f"\n🎯 TESTING MULTI-STEP PREDICTION:")
        multi_step_predictor = SimpleMultiStepPredictor(
            trajectory_predictor=trajectory_predictor,
            risk_model=risk_model,
            action_sampler=action_sampler,
            horizon=5,  # Shorter horizon for debugging
            num_candidates=3  # Fewer candidates for debugging
        )
        
        result = multi_step_predictor.predict_trajectories(sample_obs, current_state)
        
        best_traj = result['best_trajectory']
        print(f"  Predicted positions shape: {best_traj['positions'].shape}")
        print(f"  Predicted positions:")
        for i, pos in enumerate(best_traj['positions']):
            print(f"    Step {i+1}: ({pos[0]:.3f}, {pos[1]:.3f})")
        
        print(f"  Ground truth positions:")
        gt_positions = sample_pred[0, :5, :2].numpy()  # First 5 steps
        for i, pos in enumerate(gt_positions):
            print(f"    Step {i+1}: ({pos[0]:.3f}, {pos[1]:.3f})")
        
        # Compute errors
        errors = []
        for i in range(min(len(best_traj['positions']), len(gt_positions))):
            error = np.linalg.norm(best_traj['positions'][i] - gt_positions[i])
            errors.append(error)
            print(f"    Error step {i+1}: {error:.3f}m")
        
        # Visualize the issue
        print(f"\n🎨 CREATING DIAGNOSTIC VISUALIZATION:")
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 1. Trajectory comparison
        ax = axes[0]
        pred_pos = best_traj['positions']
        gt_pos = gt_positions
        
        ax.plot(pred_pos[:, 0], pred_pos[:, 1], 'r-o', linewidth=2, markersize=6, label='Predicted')
        ax.plot(gt_pos[:, 0], gt_pos[:, 1], 'g-s', linewidth=2, markersize=6, label='Ground Truth')
        ax.plot(current_state[0, 0].item(), current_state[0, 1].item(), 'ko', markersize=10, label='Start')
        
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_title('Trajectory Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Position over time
        ax = axes[1]
        steps = range(1, len(pred_pos) + 1)
        ax.plot(steps, pred_pos[:, 0], 'r-o', label='Pred X')
        ax.plot(steps, pred_pos[:, 1], 'r-s', label='Pred Y')
        ax.plot(steps, gt_pos[:, 0], 'g-o', label='GT X')
        ax.plot(steps, gt_pos[:, 1], 'g-s', label='GT Y')
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Position')
        ax.set_title('Position Over Time')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 3. Prediction errors
        ax = axes[2]
        ax.plot(range(1, len(errors) + 1), errors, 'purple', linewidth=2, marker='o')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Position Error (m)')
        ax.set_title('Prediction Error Growth')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('multistep_diagnosis.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        # Analyze the issues
        print(f"\n🔍 ISSUE ANALYSIS:")
        print(f"=" * 40)
        
        avg_error = np.mean(errors)
        max_error = np.max(errors)
        
        print(f"  Average error: {avg_error:.3f}m")
        print(f"  Maximum error: {max_error:.3f}m")
        print(f"  Error growth: {errors[-1]/errors[0]:.2f}x" if len(errors) > 1 else "")
        
        # Check for common issues
        if avg_error > 2.0:
            print(f"  ❌ ISSUE: Very high prediction errors")
        
        if max_error > 5.0:
            print(f"  ❌ ISSUE: Predictions diverging rapidly")
        
        # Check action magnitudes
        action_magnitudes = np.linalg.norm(best_traj['actions'], axis=1)
        avg_action_mag = np.mean(action_magnitudes)
        print(f"  Average action magnitude: {avg_action_mag:.3f}")
        
        if avg_action_mag > 1.0:
            print(f"  ❌ ISSUE: Actions too large")
        elif avg_action_mag < 0.01:
            print(f"  ❌ ISSUE: Actions too small")
        
        # Check risk values
        avg_risk = np.mean(best_traj['step_risks'])
        print(f"  Average step risk: {avg_risk:.4f}")
        
        if avg_risk > 0.9:
            print(f"  ❌ ISSUE: Risk values too high")
        elif avg_risk < 0.001:
            print(f"  ❌ ISSUE: Risk values too low")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error in diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """Suggest solutions based on diagnosis."""
    print(f"\n💡 SUGGESTED SOLUTIONS:")
    print(f"=" * 40)
    
    print(f"1. 🎯 IMPROVE SINGLE-STEP TRAINING:")
    print(f"   - Train longer with more data")
    print(f"   - Use better loss functions")
    print(f"   - Add regularization")
    
    print(f"\n2. 🔧 FIX MULTI-STEP IMPLEMENTATION:")
    print(f"   - Use smaller action steps")
    print(f"   - Better state update dynamics")
    print(f"   - Improved risk computation")
    
    print(f"\n3. 📊 ALTERNATIVE APPROACHES:")
    print(f"   - Train end-to-end multi-step model")
    print(f"   - Use recurrent prediction")
    print(f"   - Implement trajectory smoothing")
    
    print(f"\n4. 🎨 VISUALIZATION FIXES:")
    print(f"   - Check data normalization")
    print(f"   - Adjust plot scales")
    print(f"   - Add trajectory smoothing")

def main():
    """Run diagnosis and suggest solutions."""
    success = diagnose_multistep_issues()
    
    if success:
        suggest_solutions()
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Run this diagnosis to see specific issues")
        print(f"2. Check the generated plot: multistep_diagnosis.png")
        print(f"3. Implement suggested fixes")
        print(f"4. Consider simpler alternatives")
    else:
        print(f"\n⚠️  Diagnosis failed. Check errors above.")

if __name__ == "__main__":
    main()
