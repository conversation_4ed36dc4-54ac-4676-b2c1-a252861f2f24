#!/usr/bin/env python3
"""
Test script to verify tensor indexing is correct.
"""

import torch
import numpy as np

def test_tensor_indexing():
    """Test the tensor indexing used in training."""
    print("🧪 Testing Tensor Indexing")
    print("=" * 40)
    
    # Simulate the shapes we have
    N = 25  # Number of candidate actions
    B = 16  # Batch size
    
    # Create mock probabilities (N, B)
    probabilities = torch.randn(N, B)
    probabilities = torch.softmax(probabilities, dim=0)  # Normalize over actions
    
    # Create mock target action indices (B,)
    target_action_idx = torch.randint(0, N, (B,))
    
    print(f"Shapes:")
    print(f"  probabilities: {probabilities.shape}")
    print(f"  target_action_idx: {target_action_idx.shape}")
    
    # Test the indexing
    try:
        # This should work: probabilities[target_action_idx, torch.arange(B)]
        batch_probs = probabilities[target_action_idx, torch.arange(B)]
        print(f"  batch_probs: {batch_probs.shape}")
        print(f"✅ Indexing works correctly!")
        
        # Test argmax
        predicted_action_idx = torch.argmax(probabilities, dim=0)  # Over actions
        print(f"  predicted_action_idx: {predicted_action_idx.shape}")
        
        # Test comparison
        correct = (predicted_action_idx == target_action_idx).sum().item()
        print(f"  correct predictions: {correct}/{B}")
        
        print(f"✅ All tensor operations work correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error in tensor indexing: {e}")
        return False

def test_action_matching():
    """Test the action matching logic."""
    print("\n🧪 Testing Action Matching")
    print("=" * 40)
    
    # Simulate shapes
    N = 25  # Number of candidate actions
    B = 16  # Batch size
    
    # Create mock data
    candidate_actions = torch.randn(N, 2)  # [N, 2]
    target_action = torch.randn(B, 2)      # [B, 2]
    
    print(f"Shapes:")
    print(f"  candidate_actions: {candidate_actions.shape}")
    print(f"  target_action: {target_action.shape}")
    
    try:
        # Test action matching
        action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)  # [B, N, 2]
        print(f"  action_diffs: {action_diffs.shape}")
        
        action_distances = torch.norm(action_diffs, dim=2)  # [B, N]
        print(f"  action_distances: {action_distances.shape}")
        
        target_action_idx = torch.argmin(action_distances, dim=1)  # [B]
        print(f"  target_action_idx: {target_action_idx.shape}")
        
        print(f"✅ Action matching works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error in action matching: {e}")
        return False

def test_full_training_step():
    """Test a full training step simulation."""
    print("\n🧪 Testing Full Training Step")
    print("=" * 40)
    
    from trajectory_prediction_model import ActionSampler
    from enhanced_risk_model import TrajectoryChoiceModel, EnhancedRiskModel
    from trajectory_prediction_model import LSTMTrajectoryPredictor
    
    try:
        device = torch.device('cpu')
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        # Create test data
        B = 8   # Batch size
        T = 10  # Sequence length
        state_dim = 8
        
        obs_seq = torch.randn(B, T, state_dim)
        pred_seq = torch.randn(B, T, state_dim)
        
        # Get candidate actions
        candidate_actions = action_sampler.get_candidates(device)
        N = candidate_actions.shape[0]
        
        print(f"Test data shapes:")
        print(f"  obs_seq: {obs_seq.shape}")
        print(f"  pred_seq: {pred_seq.shape}")
        print(f"  candidate_actions: {candidate_actions.shape}")
        
        # Simulate training step
        t = 0
        current_state = obs_seq[:, -1]  # [B, state_dim]
        target_state = pred_seq[:, t]   # [B, state_dim]
        target_action = target_state[:, :2] - current_state[:, :2]  # [B, 2]
        
        print(f"Step shapes:")
        print(f"  current_state: {current_state.shape}")
        print(f"  target_action: {target_action.shape}")
        
        # Forward pass
        with torch.no_grad():
            probabilities, scores = choice_model(obs_seq, current_state, candidate_actions)
        
        print(f"Model outputs:")
        print(f"  probabilities: {probabilities.shape}")
        print(f"  scores: {scores.shape}")
        
        # Loss computation
        action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)
        action_distances = torch.norm(action_diffs, dim=2)
        target_action_idx = torch.argmin(action_distances, dim=1)
        
        batch_probs = probabilities[target_action_idx, torch.arange(B)]
        loss = -torch.log(batch_probs + 1e-8).mean()
        
        print(f"Loss computation:")
        print(f"  target_action_idx: {target_action_idx.shape}")
        print(f"  batch_probs: {batch_probs.shape}")
        print(f"  loss: {loss.item():.4f}")
        
        # Accuracy computation
        predicted_action_idx = torch.argmax(probabilities, dim=0)
        correct = (predicted_action_idx == target_action_idx).sum().item()
        accuracy = correct / B
        
        print(f"Accuracy computation:")
        print(f"  predicted_action_idx: {predicted_action_idx.shape}")
        print(f"  correct: {correct}/{B}")
        print(f"  accuracy: {accuracy:.4f}")
        
        print(f"✅ Full training step simulation successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error in full training step: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Tensor Indexing Tests")
    print("=" * 50)
    
    tests = [
        test_tensor_indexing,
        test_action_matching,
        test_full_training_step
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n📋 Test Summary")
    print("=" * 50)
    
    test_names = [
        "Tensor indexing",
        "Action matching",
        "Full training step"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    if all(results):
        print("\n🎉 All tests passed! Tensor indexing is fixed.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
