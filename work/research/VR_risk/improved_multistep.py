"""
Improved multi-step trajectory prediction with better dynamics and visualization.
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional

from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import <PERSON>hancedRiskModel, TrajectoryChoiceModel


class ImprovedMultiStepPredictor:
    """
    Improved multi-step predictor with better state dynamics and error handling.
    """
    
    def __init__(self, 
                 trajectory_predictor: LSTMTrajectoryPredictor,
                 risk_model: EnhancedRiskModel,
                 action_sampler: ActionSampler,
                 horizon: int = 10,
                 dt: float = 0.1,
                 action_scale: float = 0.1):
        """
        Initialize improved multi-step predictor.
        
        Args:
            trajectory_predictor: Trained LSTM predictor
            risk_model: Trained risk model
            action_sampler: Action sampler
            horizon: Prediction horizon
            dt: Time step size
            action_scale: Scale factor for actions
        """
        self.trajectory_predictor = trajectory_predictor
        self.risk_model = risk_model
        self.action_sampler = action_sampler
        self.horizon = horizon
        self.dt = dt
        self.action_scale = action_scale
        
        self.choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        print(f"✅ Improved multi-step predictor initialized")
        print(f"  Horizon: {horizon} steps")
        print(f"  Time step: {dt}s")
        print(f"  Action scale: {action_scale}")
    
    def predict_single_step(self, 
                           state_history: torch.Tensor,
                           current_state: torch.Tensor) -> Tuple[torch.Tensor, float]:
        """
        Predict a single step using the trained model.
        
        Args:
            state_history: [1, T, state_dim]
            current_state: [1, state_dim]
            
        Returns:
            next_state: [1, state_dim]
            confidence: float
        """
        device = current_state.device
        candidate_actions = self.action_sampler.get_candidates(device)
        
        with torch.no_grad():
            probabilities, scores = self.choice_model(
                state_history, current_state, candidate_actions
            )
        
        # Select action (use greedy for now)
        best_action_idx = torch.argmax(probabilities, dim=0).item()
        selected_action = candidate_actions[best_action_idx] * self.action_scale
        confidence = probabilities[best_action_idx, 0].item()
        
        # Update state with better dynamics
        next_state = current_state.clone()
        
        # Update pedestrian position and velocity
        current_vel = current_state[0, 2:4]
        new_vel = current_vel + selected_action * self.dt  # Velocity update
        next_state[0, :2] += new_vel * self.dt  # Position update
        next_state[0, 2:4] = new_vel  # Update velocity
        
        # Simple AV dynamics (constant velocity for now)
        av_vel = current_state[0, 6]
        next_state[0, 4] += av_vel * self.dt  # AV moves forward
        
        return next_state, confidence
    
    def predict_trajectory(self, 
                          state_history: torch.Tensor,
                          current_state: torch.Tensor,
                          use_ground_truth_comparison: bool = False,
                          ground_truth: Optional[torch.Tensor] = None) -> Dict:
        """
        Predict a complete trajectory.
        
        Args:
            state_history: [1, T, state_dim]
            current_state: [1, state_dim]
            use_ground_truth_comparison: Whether to compare with ground truth
            ground_truth: [T, state_dim] ground truth sequence
            
        Returns:
            Dictionary with prediction results
        """
        predicted_states = []
        predicted_positions = []
        confidences = []
        step_risks = []
        
        # Start prediction
        step_state = current_state.clone()
        step_history = state_history.clone()
        
        for step in range(self.horizon):
            # Predict next step
            next_state, confidence = self.predict_single_step(step_history, step_state)
            
            # Compute simple risk (distance-based)
            ped_pos = next_state[0, :2]
            av_pos = next_state[0, 4:6]
            distance = torch.norm(ped_pos - av_pos)
            risk = torch.exp(-distance / 3.0).item()  # Risk decreases with distance
            
            # Store results
            predicted_states.append(next_state[0].cpu().numpy())
            predicted_positions.append(ped_pos.cpu().numpy())
            confidences.append(confidence)
            step_risks.append(risk)
            
            # Update for next step
            step_state = next_state
            # Update history by appending new state and removing oldest
            step_history = torch.cat([step_history[:, 1:], next_state.unsqueeze(1)], dim=1)
        
        # Convert to arrays
        predicted_states = np.array(predicted_states)  # [horizon, state_dim]
        predicted_positions = np.array(predicted_positions)  # [horizon, 2]
        confidences = np.array(confidences)
        step_risks = np.array(step_risks)
        
        result = {
            'predicted_states': predicted_states,
            'predicted_positions': predicted_positions,
            'confidences': confidences,
            'step_risks': step_risks,
            'total_risk': np.sum(step_risks),
            'avg_confidence': np.mean(confidences),
            'horizon': self.horizon
        }
        
        # Add ground truth comparison if available
        if use_ground_truth_comparison and ground_truth is not None:
            gt_positions = ground_truth[:self.horizon, :2].cpu().numpy()
            
            # Compute errors
            min_len = min(len(predicted_positions), len(gt_positions))
            if min_len > 0:
                position_errors = np.linalg.norm(
                    predicted_positions[:min_len] - gt_positions[:min_len], axis=1
                )
                
                result['ground_truth'] = {
                    'positions': gt_positions,
                    'states': ground_truth[:self.horizon].cpu().numpy()
                }
                result['errors'] = {
                    'position_errors': position_errors,
                    'final_error': position_errors[-1] if len(position_errors) > 0 else 0,
                    'mean_error': np.mean(position_errors) if len(position_errors) > 0 else 0,
                    'max_error': np.max(position_errors) if len(position_errors) > 0 else 0
                }
        
        return result
    
    def visualize_prediction(self, 
                           result: Dict,
                           title: str = "Multi-Step Trajectory Prediction",
                           save_path: Optional[str] = None) -> None:
        """
        Create visualization of prediction results.
        
        Args:
            result: Result from predict_trajectory
            title: Plot title
            save_path: Optional save path
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. Trajectory plot (top-left)
        ax = axes[0, 0]
        
        pred_pos = result['predicted_positions']
        ax.plot(pred_pos[:, 0], pred_pos[:, 1], 'red', linewidth=3, 
               marker='o', markersize=5, label='Predicted')
        
        # Plot ground truth if available
        if 'ground_truth' in result:
            gt_pos = result['ground_truth']['positions']
            ax.plot(gt_pos[:, 0], gt_pos[:, 1], 'green', linewidth=2, 
                   marker='s', markersize=4, label='Ground Truth', linestyle='--')
        
        # Mark start position
        ax.plot(pred_pos[0, 0], pred_pos[0, 1], 'orange', marker='*', 
               markersize=12, label='Start')
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title('Trajectory Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Risk and confidence over time (top-right)
        ax = axes[1, 0]
        
        steps = range(1, len(result['step_risks']) + 1)
        ax.plot(steps, result['step_risks'], 'red', linewidth=2, 
               marker='o', label='Risk')
        
        ax2 = ax.twinx()
        ax2.plot(steps, result['confidences'], 'blue', linewidth=2, 
                marker='s', label='Confidence')
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Risk Score', color='red')
        ax2.set_ylabel('Confidence', color='blue')
        ax.set_title('Risk and Confidence Evolution')
        ax.grid(True, alpha=0.3)
        
        # Add legends
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
        
        # 3. Position components over time (bottom-left)
        ax = axes[0, 1]
        
        ax.plot(steps, pred_pos[:, 0], 'red', linewidth=2, 
               marker='o', label='Predicted X')
        ax.plot(steps, pred_pos[:, 1], 'red', linewidth=2, 
               marker='s', label='Predicted Y', linestyle='--')
        
        if 'ground_truth' in result:
            gt_pos = result['ground_truth']['positions']
            ax.plot(steps, gt_pos[:, 0], 'green', linewidth=2, 
                   marker='o', label='GT X', alpha=0.7)
            ax.plot(steps, gt_pos[:, 1], 'green', linewidth=2, 
                   marker='s', label='GT Y', linestyle='--', alpha=0.7)
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Position (m)')
        ax.set_title('Position Components')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. Prediction errors (bottom-right)
        ax = axes[1, 1]
        
        if 'errors' in result:
            errors = result['errors']['position_errors']
            ax.plot(range(1, len(errors) + 1), errors, 'purple', 
                   linewidth=2, marker='o')
            ax.set_xlabel('Time Step')
            ax.set_ylabel('Position Error (m)')
            ax.set_title('Prediction Error Growth')
            ax.grid(True, alpha=0.3)
            
            # Add error statistics
            mean_error = result['errors']['mean_error']
            final_error = result['errors']['final_error']
            ax.axhline(mean_error, color='orange', linestyle=':', 
                      label=f'Mean: {mean_error:.3f}m')
            ax.text(0.02, 0.98, f'Final: {final_error:.3f}m', 
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            ax.legend()
        else:
            ax.text(0.5, 0.5, 'No ground truth\navailable', 
                   transform=ax.transAxes, ha='center', va='center',
                   fontsize=12, style='italic')
            ax.set_title('Prediction Errors')
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=200, bbox_inches='tight')
        
        plt.show()
        
        # Print summary
        print(f"\n📊 PREDICTION SUMMARY:")
        print(f"  Total risk: {result['total_risk']:.4f}")
        print(f"  Average confidence: {result['avg_confidence']:.4f}")
        print(f"  Prediction horizon: {result['horizon']} steps")
        
        if 'errors' in result:
            print(f"  Final position error: {result['errors']['final_error']:.3f}m")
            print(f"  Mean position error: {result['errors']['mean_error']:.3f}m")
            print(f"  Max position error: {result['errors']['max_error']:.3f}m")


def test_improved_multistep():
    """Test the improved multi-step predictor."""
    print("🧪 Testing Improved Multi-Step Predictor")
    print("=" * 50)
    
    device = torch.device('cpu')
    
    try:
        # Load data
        from vr_data_loader import create_vr_dataloaders
        
        train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
            data_path='processed_data',
            batch_size=4,
            obs_len=10,
            pred_len=10,
            normalize=True,
            num_workers=0
        )
        
        # Get test sample
        obs_seq, pred_seq, metadata = next(iter(test_loader))
        sample_obs = obs_seq[0:1]
        sample_pred = pred_seq[0:1]
        current_state = obs_seq[0, -1].unsqueeze(0)
        
        print(f"✅ Test data loaded")
        
        # Initialize models
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        
        # Create improved predictor
        predictor = ImprovedMultiStepPredictor(
            trajectory_predictor=trajectory_predictor,
            risk_model=risk_model,
            action_sampler=action_sampler,
            horizon=8,
            dt=0.1,
            action_scale=0.05  # Smaller actions for stability
        )
        
        # Make prediction
        print(f"\n🎯 Making prediction...")
        result = predictor.predict_trajectory(
            state_history=sample_obs,
            current_state=current_state,
            use_ground_truth_comparison=True,
            ground_truth=sample_pred[0]
        )
        
        # Visualize results
        print(f"\n🎨 Creating visualization...")
        predictor.visualize_prediction(
            result=result,
            title="Improved Multi-Step Trajectory Prediction",
            save_path="improved_multistep_test.png"
        )
        
        print(f"✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_improved_multistep()
