"""
Evaluation module for LSTM-based trajectory prediction with VR data.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from torch.utils.data import DataLoader
from sklearn.metrics import mean_squared_error, mean_absolute_error
import pandas as pd

from trajectory_prediction_model import ActionSampler
from enhanced_risk_model import TrajectoryChoiceModel
from trajectory_inference import create_inference_system


class VRTrajectoryEvaluator:
    """Evaluator for LSTM trajectory prediction on VR data."""
    
    def __init__(self, model: TrajectoryChoiceModel, device: torch.device, 
                 normalization_stats: Optional[Dict] = None):
        """
        Initialize evaluator.
        
        Args:
            model: Trained TrajectoryChoiceModel
            device: Device to run evaluation on
            normalization_stats: Statistics for denormalizing data
        """
        self.model = model
        self.device = device
        self.normalization_stats = normalization_stats
        self.action_sampler = ActionSampler()
        
        # Move model to device and set to eval mode
        self.model.to(device)
        self.model.eval()
    
    def denormalize(self, data: torch.Tensor) -> torch.Tensor:
        """Denormalize data if normalization stats are available."""
        if self.normalization_stats is None:
            return data
        
        mean = torch.tensor(self.normalization_stats['mean'], device=data.device)
        std = torch.tensor(self.normalization_stats['std'], device=data.device)
        
        return data * std + mean
    
    def predict_trajectory(self, obs_seq: torch.Tensor, horizon: int = 10) -> torch.Tensor:
        """
        Predict trajectory for given observation sequence.
        
        Args:
            obs_seq: Observation sequence (batch_size, obs_len, state_dim)
            horizon: Number of steps to predict
            
        Returns:
            Predicted trajectory (batch_size, horizon, state_dim)
        """
        batch_size = obs_seq.shape[0]
        predicted_states = []
        
        current_state = obs_seq[:, -1]  # Last observation
        candidate_actions = self.action_sampler.get_candidates(self.device)
        
        with torch.no_grad():
            for t in range(horizon):
                # Get action probabilities
                probabilities, _ = self.model(obs_seq, current_state.unsqueeze(1), candidate_actions)
                
                # Sample action based on probabilities
                action_idx = torch.multinomial(probabilities[:, 0], 1).squeeze()
                selected_actions = candidate_actions[action_idx]
                
                # Apply action to get next state
                next_state = current_state.clone()
                next_state[:, :2] += selected_actions  # Update position
                
                predicted_states.append(next_state)
                current_state = next_state
        
        return torch.stack(predicted_states, dim=1)
    
    def compute_metrics(self, test_loader: DataLoader) -> Dict:
        """
        Compute evaluation metrics on test set.
        
        Args:
            test_loader: Test data loader
            
        Returns:
            Dictionary of metrics
        """
        all_predictions = []
        all_targets = []
        all_observations = []
        all_metadata = []
        
        position_errors = []
        velocity_errors = []
        
        with torch.no_grad():
            for obs_seq, pred_seq, metadata in test_loader:
                obs_seq = obs_seq.to(self.device)
                pred_seq = pred_seq.to(self.device)
                
                # Predict trajectory
                predicted_traj = self.predict_trajectory(obs_seq, horizon=pred_seq.shape[1])
                
                # Denormalize if needed
                if self.normalization_stats is not None:
                    obs_seq = self.denormalize(obs_seq)
                    pred_seq = self.denormalize(pred_seq)
                    predicted_traj = self.denormalize(predicted_traj)
                
                # Store for analysis
                all_observations.append(obs_seq.cpu())
                all_predictions.append(predicted_traj.cpu())
                all_targets.append(pred_seq.cpu())
                all_metadata.extend(metadata)
                
                # Compute errors
                pos_error = torch.norm(predicted_traj[:, :, :2] - pred_seq[:, :, :2], dim=2)
                vel_error = torch.norm(predicted_traj[:, :, 2:4] - pred_seq[:, :, 2:4], dim=2)
                
                position_errors.append(pos_error.cpu())
                velocity_errors.append(vel_error.cpu())
        
        # Concatenate all results
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        all_observations = torch.cat(all_observations, dim=0)
        position_errors = torch.cat(position_errors, dim=0)
        velocity_errors = torch.cat(velocity_errors, dim=0)
        
        # Compute aggregate metrics
        metrics = {
            'position_mae': torch.mean(position_errors).item(),
            'position_rmse': torch.sqrt(torch.mean(position_errors**2)).item(),
            'velocity_mae': torch.mean(velocity_errors).item(),
            'velocity_rmse': torch.sqrt(torch.mean(velocity_errors**2)).item(),
            'final_displacement_error': torch.mean(position_errors[:, -1]).item(),
            'average_displacement_error': torch.mean(position_errors).item()
        }
        
        # Time-wise metrics
        time_metrics = {}
        for t in range(position_errors.shape[1]):
            time_metrics[f'position_mae_t{t+1}'] = torch.mean(position_errors[:, t]).item()
            time_metrics[f'velocity_mae_t{t+1}'] = torch.mean(velocity_errors[:, t]).item()
        
        metrics.update(time_metrics)
        
        return {
            'metrics': metrics,
            'predictions': all_predictions,
            'targets': all_targets,
            'observations': all_observations,
            'metadata': all_metadata,
            'position_errors': position_errors,
            'velocity_errors': velocity_errors
        }
    
    def analyze_by_scenario(self, evaluation_results: Dict) -> Dict:
        """
        Analyze results by different scenarios (crossing vs non-crossing, brake behavior, etc.).
        
        Args:
            evaluation_results: Results from compute_metrics
            
        Returns:
            Dictionary of scenario-specific metrics
        """
        metadata = evaluation_results['metadata']
        position_errors = evaluation_results['position_errors']
        
        scenario_metrics = {}
        
        # Group by crossing behavior
        crossing_errors = []
        non_crossing_errors = []
        
        for i, meta in enumerate(metadata):
            if meta['crossing'] == 1:
                crossing_errors.append(position_errors[i])
            else:
                non_crossing_errors.append(position_errors[i])
        
        if crossing_errors:
            crossing_errors = torch.stack(crossing_errors)
            scenario_metrics['crossing'] = {
                'position_mae': torch.mean(crossing_errors).item(),
                'position_rmse': torch.sqrt(torch.mean(crossing_errors**2)).item(),
                'count': len(crossing_errors)
            }
        
        if non_crossing_errors:
            non_crossing_errors = torch.stack(non_crossing_errors)
            scenario_metrics['non_crossing'] = {
                'position_mae': torch.mean(non_crossing_errors).item(),
                'position_rmse': torch.sqrt(torch.mean(non_crossing_errors**2)).item(),
                'count': len(non_crossing_errors)
            }
        
        # Group by brake behavior
        brake_behaviors = {}
        for i, meta in enumerate(metadata):
            brake_type = meta['brake_behavior']
            if brake_type not in brake_behaviors:
                brake_behaviors[brake_type] = []
            brake_behaviors[brake_type].append(position_errors[i])
        
        scenario_metrics['brake_behaviors'] = {}
        for brake_type, errors in brake_behaviors.items():
            if errors:
                errors = torch.stack(errors)
                scenario_metrics['brake_behaviors'][brake_type] = {
                    'position_mae': torch.mean(errors).item(),
                    'position_rmse': torch.sqrt(torch.mean(errors**2)).item(),
                    'count': len(errors)
                }
        
        return scenario_metrics
    
    def plot_results(self, evaluation_results: Dict, save_dir: str = "evaluation_plots"):
        """
        Create visualization plots for evaluation results.
        
        Args:
            evaluation_results: Results from compute_metrics
            save_dir: Directory to save plots
        """
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        predictions = evaluation_results['predictions']
        targets = evaluation_results['targets']
        observations = evaluation_results['observations']
        position_errors = evaluation_results['position_errors']
        velocity_errors = evaluation_results['velocity_errors']
        
        # 1. Error over time plot
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        time_steps = range(1, position_errors.shape[1] + 1)
        pos_mean = torch.mean(position_errors, dim=0)
        pos_std = torch.std(position_errors, dim=0)
        
        plt.plot(time_steps, pos_mean, 'b-', label='Position Error')
        plt.fill_between(time_steps, pos_mean - pos_std, pos_mean + pos_std, alpha=0.3)
        plt.xlabel('Prediction Step')
        plt.ylabel('Position Error (m)')
        plt.title('Position Error Over Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        vel_mean = torch.mean(velocity_errors, dim=0)
        vel_std = torch.std(velocity_errors, dim=0)
        
        plt.plot(time_steps, vel_mean, 'r-', label='Velocity Error')
        plt.fill_between(time_steps, vel_mean - vel_std, vel_mean + vel_std, alpha=0.3)
        plt.xlabel('Prediction Step')
        plt.ylabel('Velocity Error (m/s)')
        plt.title('Velocity Error Over Time')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'error_over_time.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. Sample trajectory plots
        plt.figure(figsize=(15, 10))
        
        # Show first 6 samples
        for i in range(min(6, predictions.shape[0])):
            plt.subplot(2, 3, i + 1)
            
            # Extract trajectories
            obs_traj = observations[i, :, :2].numpy()
            pred_traj = predictions[i, :, :2].numpy()
            target_traj = targets[i, :, :2].numpy()
            
            # Plot
            plt.plot(obs_traj[:, 0], obs_traj[:, 1], 'g-o', label='Observed', markersize=3)
            plt.plot(target_traj[:, 0], target_traj[:, 1], 'b-s', label='Ground Truth', markersize=3)
            plt.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--^', label='Predicted', markersize=3)
            
            plt.xlabel('X Position (m)')
            plt.ylabel('Y Position (m)')
            plt.title(f'Sample {i+1}')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.axis('equal')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'sample_trajectories.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        # 3. Error distribution
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        final_errors = position_errors[:, -1].numpy()
        plt.hist(final_errors, bins=50, alpha=0.7, edgecolor='black')
        plt.xlabel('Final Position Error (m)')
        plt.ylabel('Frequency')
        plt.title('Distribution of Final Position Errors')
        plt.axvline(np.mean(final_errors), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(final_errors):.3f}m')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        avg_errors = torch.mean(position_errors, dim=1).numpy()
        plt.hist(avg_errors, bins=50, alpha=0.7, edgecolor='black')
        plt.xlabel('Average Position Error (m)')
        plt.ylabel('Frequency')
        plt.title('Distribution of Average Position Errors')
        plt.axvline(np.mean(avg_errors), color='red', linestyle='--',
                   label=f'Mean: {np.mean(avg_errors):.3f}m')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'error_distributions.png'), dpi=300, bbox_inches='tight')
        plt.show()
