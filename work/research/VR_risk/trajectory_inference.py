"""
Trajectory Inference and Multi-Step Rollout System

This module implements:
1. Single-step action selection (deterministic and stochastic)
2. Multi-step trajectory rollout for horizon prediction
3. Trajectory evaluation and metrics
4. Inference utilities for real-time prediction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import math
from dataclasses import dataclass


@dataclass
class TrajectoryPrediction:
    """Container for trajectory prediction results."""
    states: torch.Tensor          # (H, B, 8) - predicted states over horizon H
    actions: torch.Tensor         # (H, B, 2) - selected actions
    probabilities: torch.Tensor   # (H, N, B) - action probabilities at each step
    scores: torch.Tensor          # (H, N, B) - risk scores at each step
    confidence: torch.Tensor      # (H, B) - prediction confidence


class TrajectoryInference:
    """
    Multi-step trajectory inference with risk-aware action selection.

    Implements both:
    - Option A: Deterministic selection (argmax)
    - Option B: Stochastic sampling
    """

    def __init__(self,
                 choice_model,
                 action_sampler,
                 horizon: int = 10,
                 selection_mode: str = 'deterministic',  # 'deterministic' or 'stochastic'
                 temperature: float = 1.0):
        """
        Args:
            choice_model: TrajectoryChoiceModel instance
            action_sampler: ActionSampler instance
            horizon: Number of steps to predict ahead
            selection_mode: 'deterministic' (argmax) or 'stochastic' (sampling)
            temperature: Temperature for stochastic sampling (lower = more deterministic)
        """
        self.choice_model = choice_model
        self.action_sampler = action_sampler
        self.horizon = horizon
        self.selection_mode = selection_mode
        self.temperature = temperature

    def select_action(self,
                     probabilities: torch.Tensor,
                     scores: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Select action based on probabilities.

        Args:
            probabilities: (N, B) - action probabilities
            scores: (N, B) - risk scores

        Returns:
            selected_actions: (B,) - selected action indices
            confidence: (B,) - selection confidence
        """
        if self.selection_mode == 'deterministic':
            # Option A: argmax selection
            selected_actions = torch.argmax(probabilities, dim=0)  # (B,)
            confidence = torch.max(probabilities, dim=0)[0]  # (B,)

        elif self.selection_mode == 'stochastic':
            # Option B: stochastic sampling with temperature
            if self.temperature != 1.0:
                # Apply temperature scaling
                scaled_probs = F.softmax(torch.log(probabilities.clamp(min=1e-8)) / self.temperature, dim=0)
            else:
                scaled_probs = probabilities

            # Sample from categorical distribution
            batch_size = probabilities.shape[1]
            selected_actions = torch.zeros(batch_size, dtype=torch.long, device=probabilities.device)

            for b in range(batch_size):
                selected_actions[b] = torch.multinomial(scaled_probs[:, b], 1).squeeze()

            # Confidence is the probability of selected action
            batch_indices = torch.arange(batch_size, device=probabilities.device)
            confidence = probabilities[selected_actions, batch_indices]

        else:
            raise ValueError(f"Unknown selection mode: {self.selection_mode}")

        return selected_actions, confidence

    def apply_action_to_state(self,
                             current_state: torch.Tensor,
                             action_indices: torch.Tensor,
                             candidate_actions: torch.Tensor,
                             dt: float = 0.05) -> torch.Tensor:
        """
        Apply selected actions to current state to get next state.

        Args:
            current_state: (B, 8) - current state
            action_indices: (B,) - selected action indices
            candidate_actions: (N, 2) - all candidate actions
            dt: time step

        Returns:
            next_state: (B, 8) - next state after applying actions
        """
        batch_size = current_state.shape[0]

        # Get selected actions
        selected_actions = candidate_actions[action_indices]  # (B, 2)
        speed_change = selected_actions[:, 0]    # (B,)
        heading_change = selected_actions[:, 1]  # (B,)

        # Current state components
        ped_pos = current_state[:, :2]      # (B, 2)
        ped_vel = current_state[:, 2:4]     # (B, 2)
        av_pos = current_state[:, 4:6]      # (B, 2)
        av_speed = current_state[:, 6]      # (B,)
        av_accel = current_state[:, 7]      # (B,)

        # Update pedestrian state
        current_ped_speed = torch.norm(ped_vel, dim=1)  # (B,)
        current_ped_heading = torch.atan2(ped_vel[:, 1], ped_vel[:, 0])  # (B,)

        # Apply action
        new_ped_speed = (current_ped_speed + speed_change * dt).clamp(min=0.0)  # (B,)
        new_ped_heading = current_ped_heading + heading_change * dt  # (B,)

        # Convert to velocity components
        new_ped_vel = torch.stack([
            new_ped_speed * torch.cos(new_ped_heading),
            new_ped_speed * torch.sin(new_ped_heading)
        ], dim=1)  # (B, 2)

        # Update pedestrian position
        new_ped_pos = ped_pos + new_ped_vel * dt  # (B, 2)

        # Update AV state (simple constant velocity model)
        new_av_pos = av_pos + torch.stack([av_speed * dt, torch.zeros_like(av_speed)], dim=1)  # (B, 2)
        new_av_speed = av_speed + av_accel * dt  # (B,)

        # Construct next state
        next_state = torch.cat([
            new_ped_pos,     # (B, 2)
            new_ped_vel,     # (B, 2)
            new_av_pos,      # (B, 2)
            new_av_speed.unsqueeze(1),  # (B, 1)
            av_accel.unsqueeze(1)       # (B, 1) - assume constant acceleration
        ], dim=1)  # (B, 8)

        return next_state

    def predict_trajectory(self,
                          initial_state_history: torch.Tensor,
                          initial_current_state: torch.Tensor,
                          intended_path: Optional[torch.Tensor] = None,
                          dt: float = 0.05) -> TrajectoryPrediction:
        """
        Predict multi-step trajectory over the specified horizon.

        Args:
            initial_state_history: (B, T, 8) - initial state history
            initial_current_state: (B, 8) - initial current state
            intended_path: Optional intended path
            dt: time step

        Returns:
            TrajectoryPrediction with predicted states, actions, probabilities, etc.
        """
        device = initial_current_state.device
        batch_size = initial_current_state.shape[0]
        seq_length = initial_state_history.shape[1]

        # Get candidate actions
        candidate_actions = self.action_sampler.get_candidates(device)
        num_actions = candidate_actions.shape[0]

        # Storage for results
        predicted_states = torch.zeros(self.horizon, batch_size, 8, device=device)
        selected_actions = torch.zeros(self.horizon, batch_size, 2, device=device)
        all_probabilities = torch.zeros(self.horizon, num_actions, batch_size, device=device)
        all_scores = torch.zeros(self.horizon, num_actions, batch_size, device=device)
        confidences = torch.zeros(self.horizon, batch_size, device=device)

        # Initialize
        current_state = initial_current_state.clone()
        state_history = initial_state_history.clone()

        # Multi-step rollout
        for step in range(self.horizon):
            # Compute action probabilities for current state
            probabilities, scores = self.choice_model(
                state_history, current_state, candidate_actions, intended_path
            )

            # Select action
            action_indices, confidence = self.select_action(probabilities, scores)

            # Apply action to get next state
            next_state = self.apply_action_to_state(
                current_state, action_indices, candidate_actions, dt
            )

            # Store results
            predicted_states[step] = next_state
            selected_actions[step] = candidate_actions[action_indices]
            all_probabilities[step] = probabilities
            all_scores[step] = scores
            confidences[step] = confidence

            # Update for next iteration
            current_state = next_state

            # Update state history (sliding window)
            new_history = torch.cat([state_history[:, 1:], current_state.unsqueeze(1)], dim=1)
            state_history = new_history

        return TrajectoryPrediction(
            states=predicted_states,
            actions=selected_actions,
            probabilities=all_probabilities,
            scores=all_scores,
            confidence=confidences
        )

    def predict_multiple_trajectories(self,
                                    initial_state_history: torch.Tensor,
                                    initial_current_state: torch.Tensor,
                                    num_samples: int = 10,
                                    intended_path: Optional[torch.Tensor] = None,
                                    dt: float = 0.05) -> List[TrajectoryPrediction]:
        """
        Generate multiple trajectory samples (useful for stochastic mode).

        Args:
            initial_state_history: (B, T, 8) - initial state history
            initial_current_state: (B, 8) - initial current state
            num_samples: Number of trajectory samples to generate
            intended_path: Optional intended path
            dt: time step

        Returns:
            List of TrajectoryPrediction objects
        """
        trajectories = []

        for _ in range(num_samples):
            trajectory = self.predict_trajectory(
                initial_state_history, initial_current_state, intended_path, dt
            )
            trajectories.append(trajectory)

        return trajectories


class TrajectoryEvaluator:
    """
    Evaluate trajectory predictions against ground truth.
    """

    def __init__(self):
        pass

    def compute_metrics(self,
                       predicted_trajectory: TrajectoryPrediction,
                       ground_truth_states: torch.Tensor) -> Dict[str, float]:
        """
        Compute evaluation metrics.

        Args:
            predicted_trajectory: TrajectoryPrediction object
            ground_truth_states: (H, B, 8) - ground truth states

        Returns:
            Dictionary of metrics
        """
        pred_states = predicted_trajectory.states  # (H, B, 8)
        gt_states = ground_truth_states

        # Position metrics (pedestrian only)
        pred_pos = pred_states[:, :, :2]  # (H, B, 2)
        gt_pos = gt_states[:, :, :2]      # (H, B, 2)

        # Average Displacement Error (ADE)
        displacement_errors = torch.norm(pred_pos - gt_pos, dim=-1)  # (H, B)
        ade = displacement_errors.mean().item()

        # Final Displacement Error (FDE)
        fde = displacement_errors[-1].mean().item()

        # Root Mean Square Error (RMSE)
        mse = ((pred_pos - gt_pos) ** 2).mean()
        rmse = torch.sqrt(mse).item()

        # Speed metrics
        pred_speed = torch.norm(pred_states[:, :, 2:4], dim=-1)  # (H, B)
        gt_speed = torch.norm(gt_states[:, :, 2:4], dim=-1)      # (H, B)
        speed_mae = torch.abs(pred_speed - gt_speed).mean().item()

        # Confidence metrics
        avg_confidence = predicted_trajectory.confidence.mean().item()
        min_confidence = predicted_trajectory.confidence.min().item()

        return {
            'ADE': ade,
            'FDE': fde,
            'RMSE': rmse,
            'Speed_MAE': speed_mae,
            'Avg_Confidence': avg_confidence,
            'Min_Confidence': min_confidence
        }


def create_inference_system(trajectory_predictor,
                          risk_model,
                          action_sampler,
                          horizon: int = 10,
                          selection_mode: str = 'deterministic',
                          temperature: float = 1.0):
    """
    Factory function to create complete inference system.

    Args:
        trajectory_predictor: LSTMTrajectoryPredictor instance
        risk_model: EnhancedRiskModel instance
        action_sampler: ActionSampler instance
        horizon: Prediction horizon
        selection_mode: 'deterministic' or 'stochastic'
        temperature: Temperature for stochastic sampling

    Returns:
        TrajectoryInference instance
    """
    from enhanced_risk_model import TrajectoryChoiceModel

    choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)

    inference_system = TrajectoryInference(
        choice_model=choice_model,
        action_sampler=action_sampler,
        horizon=horizon,
        selection_mode=selection_mode,
        temperature=temperature
    )

    return inference_system


if __name__ == "__main__":
    # Test trajectory inference system
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # Import required modules
    from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
    from enhanced_risk_model import EnhancedRiskModel

    # Initialize components
    trajectory_predictor = LSTMTrajectoryPredictor().to(device)
    risk_model = EnhancedRiskModel().to(device)
    action_sampler = ActionSampler()

    # Create inference system
    inference_system = create_inference_system(
        trajectory_predictor=trajectory_predictor,
        risk_model=risk_model,
        action_sampler=action_sampler,
        horizon=10,
        selection_mode='deterministic',
        temperature=1.0
    )

    # Test data
    batch_size = 2
    seq_length = 10
    state_dim = 8
    horizon = 10

    initial_state_history = torch.randn(batch_size, seq_length, state_dim).to(device)
    initial_current_state = torch.randn(batch_size, state_dim).to(device)

    print(f"Trajectory inference system initialized on {device}")
    print(f"Prediction horizon: {horizon}")
    print(f"Selection mode: {inference_system.selection_mode}")

    # Single trajectory prediction
    print("\n=== Single Trajectory Prediction ===")
    trajectory = inference_system.predict_trajectory(
        initial_state_history, initial_current_state
    )

    print(f"Predicted states shape: {trajectory.states.shape}")
    print(f"Selected actions shape: {trajectory.actions.shape}")
    print(f"Probabilities shape: {trajectory.probabilities.shape}")
    print(f"Confidence shape: {trajectory.confidence.shape}")
    print(f"Average confidence: {trajectory.confidence.mean().item():.4f}")

    # Multiple trajectory samples (stochastic mode)
    print("\n=== Multiple Trajectory Samples ===")
    inference_system.selection_mode = 'stochastic'
    inference_system.temperature = 0.5

    trajectories = inference_system.predict_multiple_trajectories(
        initial_state_history, initial_current_state, num_samples=5
    )

    print(f"Generated {len(trajectories)} trajectory samples")
    for i, traj in enumerate(trajectories):
        print(f"Sample {i+1} - Avg confidence: {traj.confidence.mean().item():.4f}")

    # Evaluation test
    print("\n=== Trajectory Evaluation ===")
    evaluator = TrajectoryEvaluator()

    # Create dummy ground truth
    gt_states = torch.randn(horizon, batch_size, state_dim).to(device)

    metrics = evaluator.compute_metrics(trajectory, gt_states)
    print("Evaluation metrics:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")

    print("\nTrajectory inference test completed successfully!")