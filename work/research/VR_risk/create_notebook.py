#!/usr/bin/env python3
"""
Create a properly formatted <PERSON><PERSON><PERSON> notebook for trajectory prediction demo.
"""

import j<PERSON>

def create_notebook():
    """Create a simple but complete trajectory prediction demo notebook."""

    notebook = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# LSTM-Based Trajectory Prediction with Risk-Aware Action Selection\\n",
                    "\\n",
                    "This notebook demonstrates the complete trajectory prediction system.\\n",
                    "\\n",
                    "## Mathematical Formulation\\n",
                    "\\n",
                    "### 1. Joint State Definition\\n",
                    "$$x_t = (x_{ped}(t), x_{AV}(t))$$\\n",
                    "\\n",
                    "### 2. LSTM Operator\\n",
                    "- **Encoder**: LSTM maps $(T \\\\times \\\\dim(x)) \\\\rightarrow h_t$\\n",
                    "- **Action Embedding**: $\\\\phi(a_j) \\\\in \\\\mathbb{R}^H$\\n",
                    "- **Decoder**: MLP takes $[h_t; \\\\phi(a_j)] \\\\rightarrow \\\\Delta x_j$\\n",
                    "- **Prediction**: $x'_j = x_t + \\\\Delta x_j$\\n",
                    "\\n",
                    "### 3. Risk + Cost Scoring\\n",
                    "$$S_j = R(x'_j; p) + C(a_j; q)$$\\n",
                    "\\n",
                    "### 4. Choice Probability\\n",
                    "$$\\\\hat{P}(a_j|x_t) = \\\\frac{\\\\exp(-S_j)}{\\\\sum_{k=1}^N \\\\exp(-S_k)}$$\\n",
                    "\\n",
                    "### 5. Training Loss\\n",
                    "$$L = -\\\\log \\\\hat{P}(a_t|x_t)$$"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Import required libraries\\n",
                    "import torch\\n",
                    "import torch.nn as nn\\n",
                    "import torch.optim as optim\\n",
                    "import numpy as np\\n",
                    "import matplotlib.pyplot as plt\\n",
                    "import seaborn as sns\\n",
                    "from typing import List, Dict\\n",
                    "import warnings\\n",
                    "warnings.filterwarnings('ignore')\\n",
                    "\\n",
                    "# Set style\\n",
                    "plt.style.use('default')\\n",
                    "sns.set_palette(\\\"husl\\\")\\n",
                    "plt.rcParams['figure.figsize'] = (12, 8)\\n",
                    "\\n",
                    "# Device configuration\\n",
                    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\\n",
                    "print(f\\\"🚀 Using device: {device}\\\")\\n",
                    "\\n",
                    "# Import our custom modules\\n",
                    "from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler\\n",
                    "from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel\\n",
                    "from trajectory_inference import create_inference_system\\n",
                    "from trajectory_visualization import TrajectoryVisualizer"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 1. Create Synthetic Data"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "def create_synthetic_trajectories(num_trajectories: int = 20, seq_length: int = 30):\\n",
                    "    \\\"\\\"\\\"Create synthetic pedestrian-vehicle interaction trajectories.\\\"\\\"\\\"\\n",
                    "    print(f\\\"🔧 Creating {num_trajectories} synthetic trajectories...\\\")\\n",
                    "    trajectories = []\\n",
                    "    \\n",
                    "    for i in range(num_trajectories):\\n",
                    "        # Initialize random starting positions\\n",
                    "        ped_start = np.random.uniform([0, 0], [10, 10])\\n",
                    "        av_start = np.random.uniform([20, 5], [30, 15])\\n",
                    "        \\n",
                    "        states = []\\n",
                    "        ped_pos = ped_start.copy()\\n",
                    "        ped_vel = np.random.uniform([-1, -1], [1, 1])\\n",
                    "        av_pos = av_start.copy()\\n",
                    "        av_speed = np.random.uniform(5, 10)\\n",
                    "        av_accel = 0.0\\n",
                    "        \\n",
                    "        for t in range(seq_length):\\n",
                    "            # Create state vector [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]\\n",
                    "            state = np.array([\\n",
                    "                ped_pos[0], ped_pos[1],  # pedestrian position\\n",
                    "                ped_vel[0], ped_vel[1],  # pedestrian velocity\\n",
                    "                av_pos[0], av_pos[1],    # AV position\\n",
                    "                av_speed,                # AV speed\\n",
                    "                av_accel                 # AV acceleration\\n",
                    "            ])\\n",
                    "            states.append(state)\\n",
                    "            \\n",
                    "            # Simple physics update\\n",
                    "            dt = 0.1\\n",
                    "            ped_pos += ped_vel * dt\\n",
                    "            av_pos[0] -= av_speed * dt\\n",
                    "        \\n",
                    "        trajectories.append({\\n",
                    "            'states': np.array(states),\\n",
                    "            'file': f'synthetic_{i}'\\n",
                    "        })\\n",
                    "    \\n",
                    "    return trajectories\\n",
                    "\\n",
                    "# Create data\\n",
                    "trajectories = create_synthetic_trajectories()\\n",
                    "print(f\\\"✅ Created {len(trajectories)} trajectories\\\")\\n",
                    "\\n",
                    "# Show sample\\n",
                    "sample = trajectories[0]['states']\\n",
                    "print(f\\\"Sample trajectory shape: {sample.shape}\\\")\\n",
                    "print(f\\\"State vector: [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]\\\")"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 2. Initialize Models and Display Parameters"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "# Model configuration\\n",
                    "config = {\\n",
                    "    'state_dim': 8,\\n",
                    "    'hidden_dim': 64,\\n",
                    "    'num_layers': 2,\\n",
                    "    'action_embed_dim': 32,\\n",
                    "    'sequence_length': 10,\\n",
                    "    'horizon': 15\\n",
                    "}\\n",
                    "\\n",
                    "print(\\\"🏗️  Initializing models...\\\")\\n",
                    "\\n",
                    "# Initialize components\\n",
                    "trajectory_predictor = LSTMTrajectoryPredictor(\\n",
                    "    state_dim=config['state_dim'],\\n",
                    "    hidden_dim=config['hidden_dim'],\\n",
                    "    num_layers=config['num_layers'],\\n",
                    "    action_embed_dim=config['action_embed_dim']\\n",
                    ").to(device)\\n",
                    "\\n",
                    "risk_model = EnhancedRiskModel(\\n",
                    "    collision_weight=1.0,\\n",
                    "    path_deviation_weight=0.5,\\n",
                    "    behavioral_weight=0.3,\\n",
                    "    perceptual_weight=0.2\\n",
                    ").to(device)\\n",
                    "\\n",
                    "action_sampler = ActionSampler()\\n",
                    "choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)\\n",
                    "\\n",
                    "inference_system = create_inference_system(\\n",
                    "    trajectory_predictor, risk_model, action_sampler, horizon=config['horizon']\\n",
                    ")\\n",
                    "\\n",
                    "print(f\\\"✅ Models initialized!\\\")\\n",
                    "print(f\\\"   Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}\\\")\\n",
                    "print(f\\\"   Candidate actions: {action_sampler.num_actions}\\\")"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 3. Display Risk Model Parameters\\n",
                    "\\n",
                    "Let's examine the learnable parameters in our risk model."
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "print(\\\"🔍 RISK MODEL PARAMETERS\\\")\\n",
                    "print(\\\"=\\\" * 40)\\n",
                    "\\n",
                    "# Risk component weights (p parameters)\\n",
                    "print(\\\"\\\\n📊 Risk Component Weights (p parameters):\\\")\\n",
                    "print(f\\\"  Collision weight:     {risk_model.w_collision.item():.4f}\\\")\\n",
                    "print(f\\\"  Path deviation weight: {risk_model.w_path_dev.item():.4f}\\\")\\n",
                    "print(f\\\"  Behavioral weight:    {risk_model.w_behavioral.item():.4f}\\\")\\n",
                    "print(f\\\"  Perceptual weight:    {risk_model.w_perceptual.item():.4f}\\\")\\n",
                    "\\n",
                    "# Action cost weights (q parameters)\\n",
                    "print(\\\"\\\\n💰 Action Cost Weights (q parameters):\\\")\\n",
                    "print(f\\\"  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}\\\")\\n",
                    "print(f\\\"  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}\\\")\\n",
                    "\\n",
                    "# Risk function parameters\\n",
                    "print(\\\"\\\\n⚙️  Risk Function Parameters:\\\")\\n",
                    "print(f\\\"  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}\\\")\\n",
                    "print(f\\\"  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}\\\")\\n",
                    "print(f\\\"  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}\\\")\\n",
                    "print(f\\\"  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}\\\")\\n",
                    "\\n",
                    "# Visualize parameters\\n",
                    "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\\n",
                    "\\n",
                    "# Risk weights\\n",
                    "weights = [\\n",
                    "    risk_model.w_collision.item(),\\n",
                    "    risk_model.w_path_dev.item(),\\n",
                    "    risk_model.w_behavioral.item(),\\n",
                    "    risk_model.w_perceptual.item()\\n",
                    "]\\n",
                    "labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']\\n",
                    "\\n",
                    "axes[0].bar(labels, weights, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'], alpha=0.7)\\n",
                    "axes[0].set_ylabel('Weight Value')\\n",
                    "axes[0].set_title('Risk Component Weights (p)')\\n",
                    "axes[0].grid(True, alpha=0.3)\\n",
                    "\\n",
                    "# Action costs\\n",
                    "cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]\\n",
                    "cost_labels = ['Speed Cost', 'Heading Cost']\\n",
                    "\\n",
                    "axes[1].bar(cost_labels, cost_weights, color=['#FFA502', '#FF6348'], alpha=0.7)\\n",
                    "axes[1].set_ylabel('Weight Value')\\n",
                    "axes[1].set_title('Action Cost Weights (q)')\\n",
                    "axes[1].grid(True, alpha=0.3)\\n",
                    "\\n",
                    "plt.tight_layout()\\n",
                    "plt.show()"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 4. Single-Step Prediction Demo"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "print(\\\"🎯 SINGLE-STEP PREDICTION\\\")\\n",
                    "print(\\\"=\\\" * 30)\\n",
                    "\\n",
                    "# Prepare sample data\\n",
                    "sample_traj = trajectories[0]\\n",
                    "states = torch.tensor(sample_traj['states'], dtype=torch.float32).to(device)\\n",
                    "\\n",
                    "# Create sequence\\n",
                    "seq_len = config['sequence_length']\\n",
                    "start_idx = 10\\n",
                    "\\n",
                    "state_history = states[start_idx:start_idx+seq_len].unsqueeze(0)\\n",
                    "current_state = states[start_idx+seq_len].unsqueeze(0)\\n",
                    "\\n",
                    "print(f\\\"State history shape: {state_history.shape}\\\")\\n",
                    "print(f\\\"Current state shape: {current_state.shape}\\\")\\n",
                    "\\n",
                    "# Get candidate actions\\n",
                    "candidate_actions = action_sampler.get_candidates(device)\\n",
                    "print(f\\\"Candidate actions: {len(candidate_actions)}\\\")\\n",
                    "\\n",
                    "# Forward pass\\n",
                    "with torch.no_grad():\\n",
                    "    # Predict states for all actions\\n",
                    "    predicted_states, _ = choice_model.trajectory_predictor(\\n",
                    "        state_history, candidate_actions, current_state\\n",
                    "    )\\n",
                    "    \\n",
                    "    # Compute probabilities and scores\\n",
                    "    probabilities, scores = choice_model(\\n",
                    "        state_history, current_state, candidate_actions\\n",
                    "    )\\n",
                    "    \\n",
                    "    # Select best action\\n",
                    "    selected_action_idx = torch.argmax(probabilities[:, 0]).item()\\n",
                    "    selected_action = candidate_actions[selected_action_idx]\\n",
                    "\\n",
                    "print(f\\\"\\\\nResults:\\\")\\n",
                    "print(f\\\"  Selected action: {selected_action.cpu().numpy()}\\\")\\n",
                    "print(f\\\"  Action probability: {probabilities[selected_action_idx, 0].item():.4f}\\\")\\n",
                    "print(f\\\"  Risk score: {scores[selected_action_idx, 0].item():.4f}\\\")\\n",
                    "\\n",
                    "# Visualize\\n",
                    "visualizer = TrajectoryVisualizer()\\n",
                    "visualizer.plot_single_step_prediction(\\n",
                    "    state_history, current_state, predicted_states, \\n",
                    "    probabilities, scores, candidate_actions, selected_action_idx\\n",
                    ")"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 5. Multi-Step Trajectory Prediction"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "print(\\\"🚀 MULTI-STEP TRAJECTORY PREDICTION\\\")\\n",
                    "print(\\\"=\\\" * 40)\\n",
                    "\\n",
                    "# Multi-step prediction\\n",
                    "horizon = config['horizon']\\n",
                    "print(f\\\"Predicting {horizon} steps ahead...\\\")\\n",
                    "\\n",
                    "with torch.no_grad():\\n",
                    "    trajectory = inference_system.predict_trajectory(state_history, current_state)\\n",
                    "\\n",
                    "print(f\\\"\\\\nTrajectory Results:\\\")\\n",
                    "print(f\\\"  Predicted states shape: {trajectory.states.shape}\\\")\\n",
                    "print(f\\\"  Average confidence: {trajectory.confidence.mean().item():.4f}\\\")\\n",
                    "print(f\\\"  Min confidence: {trajectory.confidence.min().item():.4f}\\\")\\n",
                    "print(f\\\"  Max confidence: {trajectory.confidence.max().item():.4f}\\\")\\n",
                    "\\n",
                    "# Visualize multi-step trajectory\\n",
                    "visualizer.plot_multi_step_trajectory(trajectory, state_history)"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 6. Stochastic Sampling"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "outputs": [],
                "source": [
                    "print(\\\"🎲 STOCHASTIC TRAJECTORY SAMPLING\\\")\\n",
                    "print(\\\"=\\\" * 40)\\n",
                    "\\n",
                    "# Switch to stochastic mode\\n",
                    "inference_system.selection_mode = 'stochastic'\\n",
                    "inference_system.temperature = 0.5\\n",
                    "\\n",
                    "print(f\\\"Selection mode: {inference_system.selection_mode}\\\")\\n",
                    "print(f\\\"Temperature: {inference_system.temperature}\\\")\\n",
                    "\\n",
                    "# Generate multiple samples\\n",
                    "num_samples = 10\\n",
                    "with torch.no_grad():\\n",
                    "    trajectories_samples = inference_system.predict_multiple_trajectories(\\n",
                    "        state_history, current_state, num_samples=num_samples\\n",
                    "    )\\n",
                    "\\n",
                    "print(f\\\"\\\\nGenerated {len(trajectories_samples)} trajectory samples\\\")\\n",
                    "\\n",
                    "# Analyze diversity\\n",
                    "final_positions = []\\n",
                    "for i, traj in enumerate(trajectories_samples):\\n",
                    "    final_pos = traj.states[-1, 0, :2].cpu().numpy()\\n",
                    "    final_positions.append(final_pos)\\n",
                    "    print(f\\\"  Sample {i+1}: Final pos = [{final_pos[0]:.2f}, {final_pos[1]:.2f}]\\\")\\n",
                    "\\n",
                    "final_positions = np.array(final_positions)\\n",
                    "position_std = np.std(final_positions, axis=0)\\n",
                    "print(f\\\"\\\\nPosition diversity (std): [{position_std[0]:.3f}, {position_std[1]:.3f}]\\\")\\n",
                    "\\n",
                    "# Visualize multiple trajectories\\n",
                    "visualizer.plot_multiple_trajectories(trajectories_samples, state_history)"
                ]
            },
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "## 7. Summary\\n",
                    "\\n",
                    "This notebook demonstrated the complete LSTM-based trajectory prediction system with:\\n",
                    "\\n",
                    "✅ **Mathematical formulation validation**\\n",
                    "✅ **Risk model parameters display**\\n",
                    "✅ **Single-step prediction with risk analysis**\\n",
                    "✅ **Multi-step trajectory rollout**\\n",
                    "✅ **Stochastic uncertainty quantification**\\n",
                    "✅ **Comprehensive visualizations**\\n",
                    "\\n",
                    "The system is ready for training on real VR data and deployment in practical applications."
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.12.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }

    return notebook

if __name__ == "__main__":
    notebook = create_notebook()

    # Save the notebook
    with open('trajectory_prediction_demo.ipynb', 'w') as f:
        json.dump(notebook, f, indent=2)

    print("✅ Jupyter notebook created successfully!")
    print("📝 File: trajectory_prediction_demo.ipynb")
    print("🚀 Run with: jupyter notebook trajectory_prediction_demo.ipynb")