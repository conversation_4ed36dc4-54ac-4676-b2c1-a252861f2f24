"""
Training module for LSTM-based trajectory prediction with VR data.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import time
from tqdm import tqdm
import os
import json

from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel
from vr_data_loader import create_vr_dataloaders, get_dataset_info


class VRTrajectoryTrainer:
    """Trainer for LSTM trajectory prediction on VR data."""
    
    def __init__(self, model: TrajectoryChoiceModel, device: torch.device,
                 learning_rate: float = 1e-3, weight_decay: float = 1e-5):
        """
        Initialize trainer.
        
        Args:
            model: TrajectoryChoiceModel to train
            device: Device to train on
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for regularization
        """
        self.model = model
        self.device = device
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        
        # Move model to device
        self.model.to(device)
        
        # Initialize optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Initialize action sampler
        self.action_sampler = ActionSampler()
        
        # Training history
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
        
    def compute_loss(self, obs_seq: torch.Tensor, pred_seq: torch.Tensor) -> torch.Tensor:
        """
        Compute training loss for a batch.
        
        Args:
            obs_seq: Observation sequence (batch_size, obs_len, state_dim)
            pred_seq: Prediction sequence (batch_size, pred_len, state_dim)
            
        Returns:
            Loss tensor
        """
        batch_size, pred_len, _ = pred_seq.shape
        total_loss = 0.0
        
        # Get candidate actions
        candidate_actions = self.action_sampler.get_candidates(self.device)
        
        # For each prediction step
        for t in range(pred_len):
            # Current state (last observation + predicted states so far)
            if t == 0:
                current_state = obs_seq[:, -1]  # Last observation
            else:
                current_state = pred_seq[:, t-1]  # Previous prediction

            # Target action (difference to next state)
            target_state = pred_seq[:, t]
            target_action = target_state[:, :2] - current_state[:, :2]  # Only position change

            # Get probabilities for all candidate actions
            # Ensure current_state has shape [B, state_dim]
            if current_state.dim() == 1:
                current_state = current_state.unsqueeze(0)
            probabilities, _ = self.model(obs_seq, current_state, candidate_actions)
            
            # Find closest candidate action to target
            action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)  # [B, N, 2]
            action_distances = torch.norm(action_diffs, dim=2)  # [B, N]
            target_action_idx = torch.argmin(action_distances, dim=1)  # [B]

            # Compute negative log likelihood loss
            # probabilities shape: [N, B], we want probabilities[target_action_idx[i], i] for each i
            batch_probs = probabilities[target_action_idx, torch.arange(batch_size)]
            step_loss = -torch.log(batch_probs + 1e-8).mean()
            total_loss += step_loss
        
        return total_loss / pred_len
    
    def compute_accuracy(self, obs_seq: torch.Tensor, pred_seq: torch.Tensor) -> float:
        """
        Compute prediction accuracy for a batch.
        
        Args:
            obs_seq: Observation sequence
            pred_seq: Prediction sequence
            
        Returns:
            Accuracy as fraction of correct predictions
        """
        batch_size, pred_len, _ = pred_seq.shape
        correct_predictions = 0
        total_predictions = 0
        
        candidate_actions = self.action_sampler.get_candidates(self.device)
        
        with torch.no_grad():
            for t in range(pred_len):
                if t == 0:
                    current_state = obs_seq[:, -1]
                else:
                    current_state = pred_seq[:, t-1]

                target_state = pred_seq[:, t]
                target_action = target_state[:, :2] - current_state[:, :2]

                # Ensure current_state has shape [B, state_dim]
                if current_state.dim() == 1:
                    current_state = current_state.unsqueeze(0)
                probabilities, _ = self.model(obs_seq, current_state, candidate_actions)
                predicted_action_idx = torch.argmax(probabilities, dim=0)  # Shape: [B]

                # Find closest candidate action to target
                action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)  # [B, N, 2]
                action_distances = torch.norm(action_diffs, dim=2)  # [B, N]
                target_action_idx = torch.argmin(action_distances, dim=1)  # [B]

                correct_predictions += (predicted_action_idx == target_action_idx).sum().item()
                total_predictions += batch_size
        
        return correct_predictions / total_predictions if total_predictions > 0 else 0.0
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for obs_seq, pred_seq, metadata in pbar:
            obs_seq = obs_seq.to(self.device)
            pred_seq = pred_seq.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            loss = self.compute_loss(obs_seq, pred_seq)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # Compute accuracy
            accuracy = self.compute_accuracy(obs_seq, pred_seq)
            
            total_loss += loss.item()
            total_accuracy += accuracy
            num_batches += 1
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{accuracy:.4f}'
            })
        
        avg_loss = total_loss / num_batches
        avg_accuracy = total_accuracy / num_batches
        
        return avg_loss, avg_accuracy
    
    def validate(self, val_loader: DataLoader) -> Tuple[float, float]:
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for obs_seq, pred_seq, metadata in tqdm(val_loader, desc="Validation"):
                obs_seq = obs_seq.to(self.device)
                pred_seq = pred_seq.to(self.device)
                
                loss = self.compute_loss(obs_seq, pred_seq)
                accuracy = self.compute_accuracy(obs_seq, pred_seq)
                
                total_loss += loss.item()
                total_accuracy += accuracy
                num_batches += 1
        
        avg_loss = total_loss / num_batches
        avg_accuracy = total_accuracy / num_batches
        
        return avg_loss, avg_accuracy
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              num_epochs: int = 50, save_dir: str = "checkpoints") -> Dict:
        """
        Train the model.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            num_epochs: Number of epochs to train
            save_dir: Directory to save checkpoints
            
        Returns:
            Training history dictionary
        """
        os.makedirs(save_dir, exist_ok=True)
        
        best_val_loss = float('inf')
        patience = 10
        patience_counter = 0
        
        print(f"Starting training for {num_epochs} epochs...")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # Train
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # Validate
            val_loss, val_acc = self.validate(val_loader)
            
            # Record history
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_accuracies.append(train_acc)
            self.val_accuracies.append(val_acc)
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s)")
            print(f"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            print(f"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                
                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                    'train_acc': train_acc,
                    'val_acc': val_acc
                }
                torch.save(checkpoint, os.path.join(save_dir, 'best_model.pth'))
                print(f"  ✅ New best model saved!")
            else:
                patience_counter += 1
                
            # Early stopping
            if patience_counter >= patience:
                print(f"Early stopping after {patience} epochs without improvement")
                break
        
        # Save final model and history
        torch.save(self.model.state_dict(), os.path.join(save_dir, 'final_model.pth'))
        
        history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies
        }
        
        with open(os.path.join(save_dir, 'training_history.json'), 'w') as f:
            json.dump(history, f, indent=2)
        
        return history
