# LSTM-Based Trajectory Prediction with Risk-Aware Action Selection

This repository implements a complete trajectory prediction system based on the mathematical formulation for pedestrian-vehicle interaction scenarios.

## 📋 Mathematical Formulation

### 1. Joint State Definition
```
x_t = (x_ped(t), x_AV(t))
```
where `x_ped(t)` includes pedestrian position/velocity and `x_AV(t)` includes vehicle position, speed, acceleration.

### 2. LSTM Operator
- **Inputs**: History window `{x_{t-T+1}, ..., x_t}` of length T
- **Encoder**: LSTM maps `(T × dim(x)) → h_t`
- **Action Embedding**: For each candidate `a_j`, compute `φ(a_j) ∈ R^H`
- **Decoder**: <PERSON><PERSON> takes `[h_t; φ(a_j)] → Δx_j`
- **Prediction**: `x'_j = x_t + Δx_j`

### 3. Risk + Cost Scoring
```
S_j = R(x'_j; p) + C(a_j; q)
```
where R is multi-term risk field and C is action cost.

### 4. Choice Probability
```
P^(a_j|x_t) = exp(-S_j) / Σ_k exp(-S_k)
```

### 5. Training Loss
```
L = -log P^(a_t|x_t)
```

## 🏗️ System Architecture

### Core Components

1. **`trajectory_prediction_model.py`** - LSTM encoder-decoder with action embedding
2. **`enhanced_risk_model.py`** - Multi-term risk field and action costs
3. **`trajectory_inference.py`** - Multi-step rollout and trajectory generation
4. **`trajectory_visualization.py`** - Comprehensive visualization tools
5. **`comprehensive_demo.py`** - Complete demonstration script
6. **`trajectory_prediction_demo.ipynb`** - Interactive Jupyter notebook

### Risk Components

- **Collision Risk**: Proximity and time-to-collision based
- **Path Deviation Risk**: Distance from intended path
- **Behavioral Risk**: Speed/acceleration pattern analysis
- **Perceptual Risk**: Uncertainty in state estimation

## 🚀 Quick Start

### 1. Run the Jupyter Notebook
```bash
jupyter notebook trajectory_prediction_demo.ipynb
```

### 2. Run the Complete Demo
```bash
python comprehensive_demo.py
```

### 3. Basic Usage Example
```python
import torch
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel
from trajectory_inference import create_inference_system

# Initialize components
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
predictor = LSTMTrajectoryPredictor().to(device)
risk_model = EnhancedRiskModel().to(device)
action_sampler = ActionSampler()

# Create inference system
inference_system = create_inference_system(
    trajectory_predictor=predictor,
    risk_model=risk_model,
    action_sampler=action_sampler,
    horizon=15
)

# Predict trajectory
state_history = torch.randn(1, 10, 8).to(device)  # (batch, time, state_dim)
current_state = torch.randn(1, 8).to(device)      # (batch, state_dim)

with torch.no_grad():
    trajectory = inference_system.predict_trajectory(state_history, current_state)

print(f"Predicted {trajectory.states.shape[0]} steps")
print(f"Average confidence: {trajectory.confidence.mean():.3f}")
```

## 📊 Key Features

### ✅ Implemented Features
- [x] Complete LSTM-based trajectory prediction
- [x] Risk-aware action selection with learnable parameters
- [x] Multi-term risk field (collision, path-deviation, behavioral, perceptual)
- [x] Single-step and multi-step prediction capabilities
- [x] Stochastic trajectory sampling for uncertainty quantification
- [x] End-to-end training with cross-entropy loss
- [x] Comprehensive visualization and analysis tools
- [x] Interactive Jupyter notebook demonstration
- [x] Modular and extensible architecture

### 🎯 Model Parameters

#### Risk Component Weights (learnable)
- `w_collision`: Collision risk weight
- `w_path_dev`: Path deviation risk weight
- `w_behavioral`: Behavioral risk weight
- `w_perceptual`: Perceptual risk weight

#### Action Cost Weights (learnable)
- `w_speed_cost`: Speed change penalty
- `w_heading_cost`: Heading change penalty

#### Risk Function Parameters (learnable)
- `collision_sharpness`: Collision risk sensitivity
- `path_dev_sharpness`: Path deviation sensitivity
- `behavioral_sensitivity`: Behavioral change sensitivity
- `perceptual_noise_scale`: Perceptual uncertainty scale

## 📈 Performance Characteristics

- **Model Size**: ~50K parameters (configurable)
- **Inference Speed**: Real-time capable on GPU
- **Prediction Horizon**: Configurable (default: 15 steps)
- **Action Space**: Discrete speed/heading changes (25 actions default)
- **Uncertainty Quantification**: Via stochastic sampling

## 🎨 Visualization Features

The system includes comprehensive visualization tools:

1. **Single-step prediction analysis** with risk field visualization
2. **Multi-step trajectory plots** with confidence intervals
3. **Action probability heatmaps**
4. **Risk component breakdown**
5. **Stochastic trajectory fans** for uncertainty visualization
6. **Training progress monitoring**
7. **Interactive trajectory animations**

## 🔧 Configuration

Key configuration parameters:

```python
config = {
    'state_dim': 8,           # [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]
    'hidden_dim': 64,         # LSTM hidden dimension
    'num_layers': 2,          # Number of LSTM layers
    'action_embed_dim': 32,   # Action embedding dimension
    'sequence_length': 10,    # History window length
    'horizon': 15,            # Prediction horizon
    'learning_rate': 1e-3,    # Learning rate
}
```

## 🚀 Applications

- Autonomous vehicle trajectory planning
- Pedestrian behavior prediction in urban environments
- Robot navigation in dynamic environments
- Traffic simulation and safety analysis
- Human-robot interaction planning

## 📚 Dependencies

- PyTorch >= 1.9.0
- NumPy >= 1.19.0
- Matplotlib >= 3.3.0
- Seaborn >= 0.11.0
- Pandas >= 1.2.0 (for data loading)

## 🎯 Next Steps

1. **Train on real VR data** from your processed CSV files
2. **Extend risk components** with domain-specific knowledge
3. **Add more sophisticated action spaces** (continuous actions)
4. **Implement attention mechanisms** for better long-term dependencies
5. **Add multi-agent interactions** for complex scenarios

---

**Note**: This implementation is designed for research and educational purposes. For production use, additional validation, testing, and safety measures should be implemented.