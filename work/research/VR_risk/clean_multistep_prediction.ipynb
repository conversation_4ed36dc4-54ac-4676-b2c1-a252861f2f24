{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Clean Multi-Step Trajectory Prediction\n", "\n", "## Simple and Clean Implementation\n", "1. **<PERSON>ad trained model**\n", "2. **Predict k=5 multi-step trajectories**\n", "3. **Visualize all trajectories**\n", "4. **Compare with ground truth**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Using device: cpu\n", "✅ All modules imported successfully!\n"]}], "source": ["# Import libraries\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set device\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f'🚀 Using device: {device}')\n", "\n", "# Import our modules\n", "from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler\n", "from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel\n", "from vr_data_loader import create_vr_dataloaders\n", "\n", "print('✅ All modules imported successfully!')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 LOADING DATA AND TRAINED MODEL\n", "==================================================\n", "Found 414 CSV files\n", "Loaded 414 total sequences, 414 valid sequences\n", "Created 134481 sliding window samples\n", "Computed normalization stats:\n", "  Mean: [ 2.8491314e+01  1.4326314e+01 -1.0626707e-03 -3.9049193e-01\n", "  2.4500422e+01  1.5792528e+01  5.7381325e+00 -7.6404735e-03]\n", "  Std: [ 0.25128365  3.036767    0.45916897  1.5611238  25.790543    0.8760885\n", "  3.2571743   5.448686  ]\n", "Dataset splits:\n", "  Train: 107584 samples\n", "  Val: 13448 samples\n", "  Test: 13449 samples\n", "✅ Data loaded: 1682 test batches\n", "✅ Models initialized\n", "💾 Loading trained model...\n", "✅ Loaded model from epoch 18\n", "🎯 Model ready for prediction!\n"]}], "source": ["# Load data and trained model\n", "print('📊 LOADING DATA AND TRAINED MODEL')\n", "print('=' * 50)\n", "\n", "# Load test data\n", "train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(\n", "    data_path='processed_data',\n", "    batch_size=8,\n", "    obs_len=10,\n", "    pred_len=10,\n", "    normalize=True,\n", "    num_workers=0\n", ")\n", "\n", "print(f'✅ Data loaded: {len(test_loader)} test batches')\n", "\n", "# Initialize models\n", "trajectory_predictor = LSTMTrajectoryPredictor().to(device)\n", "risk_model = EnhancedRiskModel().to(device)\n", "action_sampler = ActionSampler()\n", "choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)\n", "\n", "print(f'✅ Models initialized')\n", "\n", "# Load trained model\n", "model_path = 'checkpoints/best_model.pth'\n", "if os.path.exists(model_path):\n", "    print(f'💾 Loading trained model...')\n", "    checkpoint = torch.load(model_path, map_location=device)\n", "    choice_model.load_state_dict(checkpoint['model_state_dict'])\n", "    print(f'✅ Loaded model from epoch {checkpoint.get(\"epoch\", \"unknown\")}')\n", "else:\n", "    print('⚠️  No saved model found - using random weights')\n", "\n", "choice_model.eval()\n", "print('🎯 Model ready for prediction!')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 SELECTING TEST SAMPLE\n", "========================================\n", "✅ Selected sample:\n", "  Subject: 12\n", "  Crossing: No\n", "  CIT: 26.130s\n", "  Ground truth shape: (10, 8)\n", "  GT ped start: (-1.177, 0.141)\n", "  GT ped end: (-0.977, -0.074)\n"]}], "source": ["# Get one test sample\n", "print('\\n🎯 SELECTING TEST SAMPLE')\n", "print('=' * 40)\n", "\n", "# Get a test batch\n", "obs_seq, pred_seq, metadata = next(iter(test_loader))\n", "\n", "# Take first sample\n", "sample_obs = obs_seq[0:1].to(device)  # [1, 10, 8]\n", "sample_pred = pred_seq[0:1].to(device)  # [1, 10, 8]\n", "sample_meta = metadata[0]\n", "\n", "print(f'✅ Selected sample:')\n", "print(f'  Subject: {sample_meta[\"subject_id\"]}')\n", "print(f'  Crossing: {\"Yes\" if sample_meta[\"crossing\"] == 1 else \"No\"}')\n", "print(f'  CIT: {sample_meta[\"CIT\"]:.3f}s')\n", "\n", "# Extract ground truth\n", "gt_trajectory = sample_pred[0].cpu().numpy()  # [10, 8]\n", "gt_ped_positions = gt_trajectory[:, :2]  # [10, 2]\n", "gt_av_positions = gt_trajectory[:, 4:6]  # [10, 2]\n", "\n", "print(f'  Ground truth shape: {gt_trajectory.shape}')\n", "print(f'  GT ped start: ({gt_ped_positions[0, 0]:.3f}, {gt_ped_positions[0, 1]:.3f})')\n", "print(f'  GT ped end: ({gt_ped_positions[-1, 0]:.3f}, {gt_ped_positions[-1, 1]:.3f})')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 GENERATING K=5 MULTI-STEP TRAJECTORIES\n", "==================================================\n", "Available actions: 25\n", "\n", "  Generating trajectory 1/5...\n", "    ✅ Greedy trajectory generated\n", "       Start: (-1.151, 0.167)\n", "       End:   (-1.151, 0.167)\n", "\n", "  Generating trajectory 2/5...\n", "    ✅ 2nd Best trajectory generated\n", "       Start: (-1.201, 0.167)\n", "       End:   (-1.651, 0.167)\n", "\n", "  Generating trajectory 3/5...\n", "    ✅ Top5-1 trajectory generated\n", "       Start: (-1.201, 0.167)\n", "       End:   (-1.501, 0.147)\n", "\n", "  Generating trajectory 4/5...\n", "    ✅ Top5-2 trajectory generated\n", "       Start: (-1.151, 0.167)\n", "       End:   (-1.551, 0.128)\n", "\n", "  Generating trajectory 5/5...\n", "    ✅ Top5-3 trajectory generated\n", "       Start: (-1.151, 0.167)\n", "       End:   (-1.551, 0.108)\n", "\n", "✅ Generated 5 trajectory predictions\n"]}], "source": ["# Generate K=5 multi-step trajectory predictions\n", "print('\\n🚀 GENERATING K=5 MULTI-STEP TRAJECTORIES')\n", "print('=' * 50)\n", "\n", "K = 5  # Number of trajectory candidates\n", "HORIZON = 10  # Prediction steps\n", "predicted_trajectories = []\n", "\n", "# Get candidate actions\n", "candidate_actions = action_sampler.get_candidates(device)\n", "print(f'Available actions: {len(candidate_actions)}')\n", "\n", "# Generate K different trajectories\n", "for k in range(K):\n", "    print(f'\\n  Generating trajectory {k+1}/{K}...')\n", "    \n", "    # Set different random seed for each trajectory\n", "    torch.manual_seed(42 + k)\n", "    \n", "    # Initialize trajectory\n", "    predicted_positions = []\n", "    predicted_states = []\n", "    \n", "    # Start from last observation\n", "    current_state = sample_obs[0, -1].unsqueeze(0)  # [1, 8]\n", "    history = sample_obs.clone()  # [1, 10, 8]\n", "    \n", "    # Generate 10-step trajectory\n", "    for step in range(HORIZON):\n", "        with torch.no_grad():\n", "            # Get action probabilities from trained model\n", "            probabilities, scores = choice_model(\n", "                history, current_state, candidate_actions\n", "            )\n", "        \n", "        # Select action based on different strategies\n", "        if k == 0:  # G<PERSON>y\n", "            action_idx = torch.argmax(probabilities, dim=0).item()\n", "        elif k == 1:  # Second best\n", "            _, top_indices = torch.topk(probabilities[:, 0], 2)\n", "            action_idx = top_indices[1].item() if len(top_indices) > 1 else 0\n", "        else:  # Random from top 5\n", "            _, top_indices = torch.topk(probabilities[:, 0], min(5, len(probabilities)))\n", "            rand_idx = torch.randint(0, len(top_indices), (1,)).item()\n", "            action_idx = top_indices[rand_idx].item()\n", "        \n", "        # Get selected action\n", "        selected_action = candidate_actions[action_idx]  # [2]\n", "        \n", "        # Simple state update\n", "        next_state = current_state.clone()\n", "        \n", "        # Update pedestrian position (first 2 elements)\n", "        action_scale = 0.05  # Small movements\n", "        next_state[0, 0] += selected_action[0] * action_scale  # X position\n", "        next_state[0, 1] += selected_action[1] * action_scale  # Y position\n", "        \n", "        # Update pedestrian velocity (elements 2-3)\n", "        next_state[0, 2] = selected_action[0] * action_scale  # X velocity\n", "        next_state[0, 3] = selected_action[1] * action_scale  # Y velocity\n", "        \n", "        # Update AV position (elements 4-5) - simple forward motion\n", "        next_state[0, 4] += 0.1  # AV moves forward\n", "        \n", "        # Store results\n", "        predicted_positions.append(next_state[0, :2].cpu().numpy())  # [2]\n", "        predicted_states.append(next_state[0].cpu().numpy())  # [8]\n", "        \n", "        # Update for next step\n", "        current_state = next_state\n", "        # Update history\n", "        history = torch.cat([history[:, 1:], next_state.unsqueeze(1)], dim=1)\n", "    \n", "    # Store complete trajectory\n", "    predicted_trajectories.append({\n", "        'id': k,\n", "        'strategy': ['Greedy', '2nd Best', 'Top5-1', 'Top5-2', 'Top5-3'][k],\n", "        'positions': np.array(predicted_positions),  # [10, 2]\n", "        'states': np.array(predicted_states)  # [10, 8]\n", "    })\n", "    \n", "    print(f'    ✅ {predicted_trajectories[-1][\"strategy\"]} trajectory generated')\n", "    print(f'       Start: ({predicted_positions[0][0]:.3f}, {predicted_positions[0][1]:.3f})')\n", "    print(f'       End:   ({predicted_positions[-1][0]:.3f}, {predicted_positions[-1][1]:.3f})')\n", "\n", "print(f'\\n✅ Generated {len(predicted_trajectories)} trajectory predictions')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🎨 VISUALIZING ALL K TRAJECTORIES\n", "========================================\n"]}, {"data": {"image/png": "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*********************************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Visualization completed!\n"]}], "source": ["# Visualize all K trajectories\n", "print('\\n🎨 VISUALIZING ALL K TRAJECTORIES')\n", "print('=' * 40)\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Colors for different trajectories\n", "colors = ['red', 'blue', 'green', 'purple', 'orange']\n", "\n", "# Plot 1: All predicted trajectories\n", "ax = axes[0, 0]\n", "\n", "for i, traj in enumerate(predicted_trajectories):\n", "    positions = traj['positions']\n", "    strategy = traj['strategy']\n", "    \n", "    ax.plot(positions[:, 0], positions[:, 1], \n", "           color=colors[i], linewidth=3, marker='o', markersize=5,\n", "           label=f'{strategy}', alpha=0.8)\n", "    \n", "    # Mark start and end\n", "    ax.plot(positions[0, 0], positions[0, 1], \n", "           color=colors[i], marker='s', markersize=8)\n", "    ax.plot(positions[-1, 0], positions[-1, 1], \n", "           color=colors[i], marker='^', markersize=8)\n", "\n", "# Plot ground truth\n", "ax.plot(gt_ped_positions[:, 0], gt_ped_positions[:, 1], \n", "       'black', linewidth=4, marker='D', markersize=6,\n", "       label='Ground Truth', linestyle='--', alpha=0.9)\n", "\n", "# Mark observation end\n", "obs_end = sample_obs[0, -1, :2].cpu().numpy()\n", "ax.plot(obs_end[0], obs_end[1], \n", "       'gold', marker='*', markersize=15, label='Obs End')\n", "\n", "ax.set_xlabel('X Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_ylabel('Y Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_title('All K=5 Trajectory Predictions', fontsize=14, fontweight='bold')\n", "ax.legend(fontsize=10)\n", "ax.grid(True, alpha=0.3)\n", "ax.axis('equal')\n", "\n", "# Plot 2: Best trajectory vs ground truth\n", "ax = axes[0, 1]\n", "\n", "best_traj = predicted_trajectories[0]  # Greedy is usually best\n", "best_pos = best_traj['positions']\n", "\n", "ax.plot(best_pos[:, 0], best_pos[:, 1], \n", "       'red', linewidth=4, marker='o', markersize=6, label='Best Prediction')\n", "ax.plot(gt_ped_positions[:, 0], gt_ped_positions[:, 1], \n", "       'green', linewidth=3, marker='s', markersize=5, \n", "       label='Ground Truth', linestyle='--')\n", "\n", "# Add step numbers\n", "for step in range(0, len(best_pos), 2):\n", "    ax.annotate(f'{step+1}', best_pos[step], xytext=(5, 5), \n", "               textcoords='offset points', fontsize=9)\n", "\n", "ax.set_xlabel('X Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_ylabel('Y Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_title('Best Prediction vs Ground Truth', fontsize=14, fontweight='bold')\n", "ax.legend(fontsize=10)\n", "ax.grid(True, alpha=0.3)\n", "ax.axis('equal')\n", "\n", "# Plot 3: Position errors over time\n", "ax = axes[1, 0]\n", "\n", "for i, traj in enumerate(predicted_trajectories):\n", "    positions = traj['positions']\n", "    errors = np.linalg.norm(positions - gt_ped_positions, axis=1)\n", "    steps = range(1, len(errors) + 1)\n", "    \n", "    ax.plot(steps, errors, color=colors[i], linewidth=2, \n", "           marker='o', markersize=4, label=traj['strategy'])\n", "\n", "ax.set_xlabel('Prediction Step', fontsize=12, fontweight='bold')\n", "ax.set_ylabel('Position Error (m)', fontsize=12, fontweight='bold')\n", "ax.set_title('Prediction Errors Over Time', fontsize=14, fontweight='bold')\n", "ax.legend(fontsize=10)\n", "ax.grid(True, alpha=0.3)\n", "\n", "# Plot 4: Final position comparison\n", "ax = axes[1, 1]\n", "\n", "# Plot final positions\n", "final_positions = np.array([traj['positions'][-1] for traj in predicted_trajectories])\n", "ax.scatter(final_positions[:, 0], final_positions[:, 1], \n", "          c=colors[:len(final_positions)], s=100, alpha=0.8, \n", "          edgecolors='black', linewidth=2)\n", "\n", "# Ground truth final position\n", "ax.plot(gt_ped_positions[-1, 0], gt_ped_positions[-1, 1], \n", "       'black', marker='*', markersize=20, label='GT Final')\n", "\n", "# Add labels\n", "for i, (pos, traj) in enumerate(zip(final_positions, predicted_trajectories)):\n", "    ax.annotate(traj['strategy'], pos, xytext=(5, 5), \n", "               textcoords='offset points', fontsize=9)\n", "\n", "ax.set_xlabel('Final X Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_ylabel('Final Y Position (m)', fontsize=12, fontweight='bold')\n", "ax.set_title('Final Position Comparison', fontsize=14, fontweight='bold')\n", "ax.legend(fontsize=10)\n", "ax.grid(True, alpha=0.3)\n", "ax.axis('equal')\n", "\n", "plt.suptitle(f'Multi-Step Trajectory Prediction Analysis\\n'\n", "            f'Subject: {sample_meta[\"subject_id\"]}, '\n", "            f'Crossing: {\"Yes\" if sample_meta[\"crossing\"] else \"No\"}', \n", "            fontsize=16, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.savefig('clean_multistep_trajectories.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print('✅ Visualization completed!')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "f-string: expecting '}' (4085013550.py, line 8)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mprint(f'{'Strategy':<12} {'Final Error':<12} {'Mean Error':<12} {'RMSE':<12} {'Max Error':<12}')\u001b[39m\n              ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m f-string: expecting '}'\n"]}], "source": ["# Evaluate trajectory predictions\n", "print('\\n📊 TRAJECTORY EVALUATION')\n", "print('=' * 40)\n", "\n", "# Compute metrics for each trajectory\n", "print(f'\\n📈 PERFORMANCE METRICS:')\n", "print(f'{\"-\"*70}')\n", "print(f'{'Strategy':<12} {'Final Error':<12} {'Mean Error':<12} {'RMSE':<12} {'Max Error':<12}'.format('Strategy', 'Final Error', 'Mean Error', 'RMSE', 'Max Error'))\n", "print(f'{\"-\"*70}')\n", "\n", "best_strategy = None\n", "best_final_error = float('inf')\n", "\n", "for traj in predicted_trajectories:\n", "    positions = traj['positions']\n", "    strategy = traj['strategy']\n", "    \n", "    # Compute errors\n", "    errors = np.linalg.norm(positions - gt_ped_positions, axis=1)\n", "    final_error = errors[-1]\n", "    mean_error = np.mean(errors)\n", "    rmse = np.sqrt(np.mean(errors**2))\n", "    max_error = np.max(errors)\n", "    \n", "    print(f'{strategy:<12} {final_error:<12.4f} {mean_error:<12.4f} {rmse:<12.4f} {max_error:<12.4f}')\n", "    \n", "    if final_error < best_final_error:\n", "        best_final_error = final_error\n", "        best_strategy = strategy\n", "\n", "print(f'{\"-\"*70}')\n", "\n", "# Overall assessment\n", "all_final_errors = []\n", "for traj in predicted_trajectories:\n", "    positions = traj['positions']\n", "    final_error = np.linalg.norm(positions[-1] - gt_ped_positions[-1])\n", "    all_final_errors.append(final_error)\n", "\n", "avg_final_error = np.mean(all_final_errors)\n", "std_final_error = np.std(all_final_errors)\n", "\n", "print(f'\\n🎯 OVERALL ASSESSMENT:')\n", "print(f'  Best strategy: {best_strategy}')\n", "print(f'  Best final error: {best_final_error:.4f}m')\n", "print(f'  Average final error: {avg_final_error:.4f} ± {std_final_error:.4f}m')\n", "print(f'  Error range: {min(all_final_errors):.4f}m - {max(all_final_errors):.4f}m')\n", "\n", "# Model assessment\n", "if avg_final_error < 0.5:\n", "    assessment = \"✅ EXCELLENT - Model predictions are very accurate\"\n", "elif avg_final_error < 1.0:\n", "    assessment = \"✅ GOOD - Model predictions are reasonably accurate\"\n", "elif avg_final_error < 2.0:\n", "    assessment = \"⚠️  MODERATE - Model has some prediction errors\"\n", "else:\n", "    assessment = \"❌ POOR - Model predictions have significant errors\"\n", "\n", "print(f'\\n🏆 MODEL ASSESSMENT: {assessment}')\n", "\n", "# Ground truth info\n", "gt_length = np.sum(np.linalg.norm(np.diff(gt_ped_positions, axis=0), axis=1))\n", "print(f'\\n📏 GROUND TRUTH INFO:')\n", "print(f'  Trajectory length: {gt_length:.4f}m')\n", "print(f'  Start position: ({gt_ped_positions[0, 0]:.3f}, {gt_ped_positions[0, 1]:.3f})')\n", "print(f'  End position: ({gt_ped_positions[-1, 0]:.3f}, {gt_ped_positions[-1, 1]:.3f})')\n", "\n", "print(f'\\n🎉 MULTI-STEP TRAJECTORY PREDICTION COMPLETED!')\n", "print(f'\\n📋 SUMMARY:')\n", "print(f'  ✅ Generated {K} different trajectory predictions')\n", "print(f'  ✅ Used trained model with different action selection strategies')\n", "print(f'  ✅ Best prediction error: {best_final_error:.4f}m')\n", "print(f'  ✅ Model assessment: {assessment.split(\" - \")[1]}')\n", "print(f'  ✅ All trajectories visualized and compared with ground truth')"]}], "metadata": {"kernelspec": {"display_name": "vr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}