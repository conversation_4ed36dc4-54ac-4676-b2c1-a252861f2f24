"""
Enhanced Risk Model for Trajectory Prediction

This module implements the multi-term risk field R(x'j; p) and action cost C(aj; q)
as specified in the formulation:

Risk Components:
1. Collision Risk - proximity and TTC-based
2. Path Deviation Risk - distance from intended path
3. Behavioral Risk - based on speed/acceleration patterns
4. Perceptual Risk - uncertainty in state estimation

Action Cost:
- Speed and heading change penalties
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import math


class EnhancedRiskModel(nn.Module):
    """
    Enhanced risk model with learnable parameters for trajectory prediction.

    Computes: S_j = R(x'_j; p) + C(a_j; q)
    where R is multi-term risk and C is action cost.
    """

    def __init__(self,
                 # Risk component weights (learnable)
                 collision_weight: float = 1.0,
                 path_deviation_weight: float = 1.0,
                 behavioral_weight: float = 1.0,
                 perceptual_weight: float = 1.0,
                 # Action cost weights (learnable)
                 speed_cost_weight: float = 0.1,
                 heading_cost_weight: float = 0.1,
                 # Risk function parameters
                 collision_threshold: float = 2.0,
                 max_path_deviation: float = 10.0,
                 ttc_threshold: float = 5.0):
        super().__init__()

        # Learnable risk weights (p parameters)
        self.w_collision = nn.Parameter(torch.tensor(collision_weight))
        self.w_path_dev = nn.Parameter(torch.tensor(path_deviation_weight))
        self.w_behavioral = nn.Parameter(torch.tensor(behavioral_weight))
        self.w_perceptual = nn.Parameter(torch.tensor(perceptual_weight))

        # Learnable action cost weights (q parameters)
        self.w_speed_cost = nn.Parameter(torch.tensor(speed_cost_weight))
        self.w_heading_cost = nn.Parameter(torch.tensor(heading_cost_weight))

        # Risk function parameters
        self.collision_threshold = collision_threshold
        self.max_path_deviation = max_path_deviation
        self.ttc_threshold = ttc_threshold

        # Additional learnable parameters for risk functions
        self.collision_sharpness = nn.Parameter(torch.tensor(2.0))
        self.path_dev_sharpness = nn.Parameter(torch.tensor(1.0))
        self.behavioral_sensitivity = nn.Parameter(torch.tensor(1.0))
        self.perceptual_noise_scale = nn.Parameter(torch.tensor(0.1))

    def collision_risk(self, predicted_states: torch.Tensor) -> torch.Tensor:
        """
        Compute collision risk based on pedestrian-vehicle proximity and TTC.

        Args:
            predicted_states: (N, B, 8) - [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]

        Returns:
            risk: (N, B) - collision risk scores
        """
        # Extract positions and velocities
        ped_pos = predicted_states[..., :2]  # (N, B, 2)
        ped_vel = predicted_states[..., 2:4]  # (N, B, 2)
        av_pos = predicted_states[..., 4:6]   # (N, B, 2)
        av_speed = predicted_states[..., 6]   # (N, B)

        # Distance between pedestrian and AV
        distance = torch.norm(ped_pos - av_pos, dim=-1)  # (N, B)

        # Relative velocity for TTC calculation
        ped_speed = torch.norm(ped_vel, dim=-1)  # (N, B)
        relative_speed = (ped_speed + av_speed).clamp(min=0.1)  # Avoid division by zero

        # Time to collision (TTC)
        ttc = distance / relative_speed  # (N, B)

        # Proximity risk (exponential decay with distance)
        proximity_risk = torch.exp(-self.collision_sharpness * distance / self.collision_threshold)

        # TTC risk (higher risk for lower TTC)
        ttc_risk = torch.exp(-ttc / self.ttc_threshold)

        # Combined collision risk
        collision_risk = proximity_risk * ttc_risk

        return collision_risk

    def path_deviation_risk(self, predicted_states: torch.Tensor,
                          intended_path: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute path deviation risk.

        Args:
            predicted_states: (N, B, 8) - predicted states
            intended_path: Optional path waypoints for deviation calculation

        Returns:
            risk: (N, B) - path deviation risk scores
        """
        ped_pos = predicted_states[..., :2]  # (N, B, 2)

        if intended_path is None:
            # Simple heuristic: assume intended path is straight ahead
            # In practice, this would use the actual intended path
            deviation = torch.norm(ped_pos - ped_pos.mean(dim=0, keepdim=True), dim=-1)
        else:
            # Compute distance to nearest point on intended path
            # This is a simplified version - in practice would use proper path projection
            deviation = torch.norm(ped_pos - intended_path.unsqueeze(0), dim=-1).min(dim=-1)[0]

        # Normalize and apply sharpness
        normalized_deviation = deviation / self.max_path_deviation
        path_risk = torch.tanh(self.path_dev_sharpness * normalized_deviation)

        return path_risk

    def behavioral_risk(self, predicted_states: torch.Tensor,
                       state_history: torch.Tensor) -> torch.Tensor:
        """
        Compute behavioral risk based on speed and acceleration patterns.

        Args:
            predicted_states: (N, B, 8) - predicted states
            state_history: (B, T, 8) - historical states for comparison

        Returns:
            risk: (N, B) - behavioral risk scores
        """
        # Extract current and predicted speeds
        current_ped_speed = torch.norm(state_history[:, -1, 2:4], dim=-1)  # (B,)
        predicted_ped_speed = torch.norm(predicted_states[..., 2:4], dim=-1)  # (N, B)

        # Speed change magnitude
        speed_change = torch.abs(predicted_ped_speed - current_ped_speed.unsqueeze(0))  # (N, B)

        # AV acceleration
        av_accel = predicted_states[..., 7]  # (N, B)

        # Behavioral risk increases with sudden speed changes and high AV acceleration
        speed_risk = torch.tanh(self.behavioral_sensitivity * speed_change)
        accel_risk = torch.tanh(self.behavioral_sensitivity * torch.abs(av_accel))

        behavioral_risk = 0.5 * (speed_risk + accel_risk)

        return behavioral_risk

    def perceptual_risk(self, predicted_states: torch.Tensor) -> torch.Tensor:
        """
        Compute perceptual uncertainty risk.

        Args:
            predicted_states: (N, B, 8) - predicted states

        Returns:
            risk: (N, B) - perceptual risk scores
        """
        # Add noise to simulate perceptual uncertainty
        noise = torch.randn_like(predicted_states) * self.perceptual_noise_scale
        noisy_states = predicted_states + noise

        # Compute variance in predictions as uncertainty measure
        state_variance = torch.var(noisy_states, dim=0).mean(dim=-1)  # (B,)

        # Expand to match shape
        perceptual_risk = state_variance.unsqueeze(0).expand(predicted_states.shape[0], -1)  # (N, B)

        return perceptual_risk

    def action_cost(self, actions: torch.Tensor) -> torch.Tensor:
        """
        Compute action cost C(aj; q).

        Args:
            actions: (N, 2) - [speed_change, heading_change]

        Returns:
            cost: (N,) - action cost scores
        """
        speed_change = actions[:, 0]    # (N,)
        heading_change = actions[:, 1]  # (N,)

        # Quadratic costs for smooth control
        speed_cost = self.w_speed_cost * (speed_change ** 2)
        heading_cost = self.w_heading_cost * (heading_change ** 2)

        total_cost = speed_cost + heading_cost

        return total_cost

    def forward(self,
                predicted_states: torch.Tensor,
                actions: torch.Tensor,
                state_history: torch.Tensor,
                intended_path: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute total risk + cost scores: S_j = R(x'_j; p) + C(a_j; q)

        Args:
            predicted_states: (N, B, 8) - predicted states x'_j
            actions: (N, 2) - candidate actions a_j
            state_history: (B, T, 8) - historical states for context
            intended_path: Optional intended path for deviation calculation

        Returns:
            scores: (N, B) - total risk + cost scores S_j
        """
        # Compute risk components
        r_collision = self.collision_risk(predicted_states)
        r_path_dev = self.path_deviation_risk(predicted_states, intended_path)
        r_behavioral = self.behavioral_risk(predicted_states, state_history)
        r_perceptual = self.perceptual_risk(predicted_states)

        # Weighted risk sum
        total_risk = (self.w_collision * r_collision +
                     self.w_path_dev * r_path_dev +
                     self.w_behavioral * r_behavioral +
                     self.w_perceptual * r_perceptual)

        # Action costs (broadcast to match batch dimension)
        action_costs = self.action_cost(actions)  # (N,)
        action_costs_expanded = action_costs.unsqueeze(1).expand(-1, predicted_states.shape[1])  # (N, B)

        # Total score: S_j = R + C
        total_scores = total_risk + action_costs_expanded

        return total_scores


class TrajectoryChoiceModel(nn.Module):
    """
    Complete trajectory choice model implementing the formulation:
    P^(aj|xt) = exp(-Sj) / Σ_k exp(-Sk)
    """

    def __init__(self, trajectory_predictor, risk_model):
        super().__init__()
        self.trajectory_predictor = trajectory_predictor
        self.risk_model = risk_model

    def compute_choice_probabilities(self,
                                   state_history: torch.Tensor,
                                   current_state: torch.Tensor,
                                   candidate_actions: torch.Tensor,
                                   intended_path: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute choice probabilities P^(aj|xt).

        Args:
            state_history: (B, T, 8) - historical states
            current_state: (B, 8) - current state xt
            candidate_actions: (N, 2) - candidate actions
            intended_path: Optional intended path

        Returns:
            probabilities: (N, B) - P^(aj|xt)
            scores: (N, B) - raw scores S_j
        """
        # Predict states for each action
        predicted_states, _ = self.trajectory_predictor(state_history, candidate_actions, current_state)

        # Compute risk + cost scores
        scores = self.risk_model(predicted_states, candidate_actions, state_history, intended_path)

        # Convert to probabilities: P^(aj|xt) = exp(-Sj) / Σ_k exp(-Sk)
        neg_scores = -scores  # (N, B)
        probabilities = F.softmax(neg_scores, dim=0)  # Softmax over actions (dim 0)

        return probabilities, scores

    def forward(self,
                state_history: torch.Tensor,
                current_state: torch.Tensor,
                candidate_actions: torch.Tensor,
                intended_path: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning probabilities and scores."""
        return self.compute_choice_probabilities(state_history, current_state, candidate_actions, intended_path)


def cross_entropy_loss(probabilities: torch.Tensor,
                      ground_truth_actions: torch.Tensor) -> torch.Tensor:
    """
    Compute cross-entropy loss: L = -log P^(at|xt)

    Args:
        probabilities: (N, B) - action probabilities
        ground_truth_actions: (B,) - ground truth action indices

    Returns:
        loss: scalar - cross-entropy loss
    """
    # Gather probabilities for ground truth actions
    batch_size = probabilities.shape[1]
    batch_indices = torch.arange(batch_size, device=probabilities.device)

    # Get probabilities for ground truth actions
    gt_probs = probabilities[ground_truth_actions, batch_indices]  # (B,)

    # Compute negative log likelihood
    loss = -torch.log(gt_probs.clamp(min=1e-8)).mean()

    return loss


if __name__ == "__main__":
    # Test the enhanced risk model
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # Initialize models
    from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler

    trajectory_predictor = LSTMTrajectoryPredictor().to(device)
    risk_model = EnhancedRiskModel().to(device)
    choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model).to(device)
    action_sampler = ActionSampler()

    # Test data
    batch_size = 4
    seq_length = 10
    state_dim = 8

    state_history = torch.randn(batch_size, seq_length, state_dim).to(device)
    current_state = torch.randn(batch_size, state_dim).to(device)
    candidate_actions = action_sampler.get_candidates(device)

    print(f"Enhanced risk model initialized on {device}")
    print(f"Number of candidate actions: {action_sampler.num_actions}")

    # Forward pass
    probabilities, scores = choice_model(state_history, current_state, candidate_actions)

    print(f"Choice probabilities shape: {probabilities.shape}")
    print(f"Risk scores shape: {scores.shape}")
    print(f"Probability sum per batch: {probabilities.sum(dim=0)}")  # Should be ~1.0

    # Test loss computation
    gt_actions = torch.randint(0, action_sampler.num_actions, (batch_size,)).to(device)
    loss = cross_entropy_loss(probabilities, gt_actions)
    print(f"Cross-entropy loss: {loss.item():.4f}")

    print("Enhanced risk model test completed successfully!")