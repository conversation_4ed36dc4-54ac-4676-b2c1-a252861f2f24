"""
LSTM-based Trajectory Prediction Model with Risk-Aware Action Selection

This module implements the formulation:
1. Joint State: xt = (xped(t), xAV(t))
2. LSTM Encoder-Decoder with Action Embedding
3. Risk + Cost Scoring
4. Probabilistic Action Selection
5. End-to-End Training
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import math


class ActionEmbedding(nn.Module):
    """
    Embeds discrete actions into continuous space.
    Actions are (speed_change, heading_change) pairs.
    """
    def __init__(self, embedding_dim: int = 32):
        super().__init__()
        self.embedding_dim = embedding_dim
        # Simple MLP to embed 2D action to H-dimensional space
        self.embed = nn.Sequential(
            nn.Linear(2, embedding_dim),
            nn.ReLU(),
            nn.Linear(embedding_dim, embedding_dim)
        )

    def forward(self, actions: torch.Tensor) -> torch.Tensor:
        """
        Args:
            actions: (N, 2) tensor of [speed_change, heading_change]
        Returns:
            embeddings: (N, H) tensor
        """
        return self.embed(actions)


class LSTMTrajectoryPredictor(nn.Module):
    """
    LSTM Encoder-Decoder for trajectory prediction with action embedding.

    Architecture:
    1. Encoder: LSTM maps (T × dim(x)) → ht
    2. Action embedding: ϕ(aj) ∈ R^H
    3. Decoder: MLP takes [ht; ϕ(aj)] → Δxj
    4. Prediction: x'j = xt + Δxj
    """

    def __init__(self,
                 state_dim: int = 8,  # [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]
                 hidden_dim: int = 64,
                 num_layers: int = 2,
                 action_embed_dim: int = 32,
                 dropout: float = 0.1):
        super().__init__()

        self.state_dim = state_dim
        self.hidden_dim = hidden_dim
        self.action_embed_dim = action_embed_dim

        # LSTM Encoder
        self.encoder = nn.LSTM(
            input_size=state_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # Action Embedding
        self.action_embedding = ActionEmbedding(action_embed_dim)

        # Decoder: [ht; ϕ(aj)] → Δxj
        decoder_input_dim = hidden_dim + action_embed_dim
        self.decoder = nn.Sequential(
            nn.Linear(decoder_input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, state_dim)  # Predict state delta
        )

    def encode_history(self, state_history: torch.Tensor) -> torch.Tensor:
        """
        Encode state history using LSTM.

        Args:
            state_history: (B, T, state_dim) - batch of state sequences

        Returns:
            ht: (B, hidden_dim) - encoded representation
        """
        # LSTM forward pass
        lstm_out, (hn, cn) = self.encoder(state_history)

        # Use the last hidden state as encoding
        ht = lstm_out[:, -1, :]  # (B, hidden_dim)

        return ht

    def predict_state_deltas(self,
                           ht: torch.Tensor,
                           candidate_actions: torch.Tensor) -> torch.Tensor:
        """
        Predict state deltas for candidate actions.

        Args:
            ht: (B, hidden_dim) - encoded state history
            candidate_actions: (N, 2) - candidate actions [speed_change, heading_change]

        Returns:
            state_deltas: (N, B, state_dim) - predicted state changes
        """
        B = ht.shape[0]
        N = candidate_actions.shape[0]

        # Embed actions: (N, action_embed_dim)
        action_embeds = self.action_embedding(candidate_actions)

        # Expand for batch processing
        ht_expanded = ht.unsqueeze(0).expand(N, B, -1)  # (N, B, hidden_dim)
        action_embeds_expanded = action_embeds.unsqueeze(1).expand(N, B, -1)  # (N, B, action_embed_dim)

        # Concatenate: [ht; ϕ(aj)]
        decoder_input = torch.cat([ht_expanded, action_embeds_expanded], dim=-1)  # (N, B, hidden_dim + action_embed_dim)

        # Reshape for decoder
        decoder_input_flat = decoder_input.view(N * B, -1)

        # Predict deltas
        deltas_flat = self.decoder(decoder_input_flat)  # (N*B, state_dim)

        # Reshape back
        state_deltas = deltas_flat.view(N, B, self.state_dim)

        return state_deltas

    def forward(self,
                state_history: torch.Tensor,
                candidate_actions: torch.Tensor,
                current_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Full forward pass: encode history and predict next states.

        Args:
            state_history: (B, T, state_dim) - batch of state sequences
            candidate_actions: (N, 2) - candidate actions
            current_state: (B, state_dim) - current state xt

        Returns:
            predicted_states: (N, B, state_dim) - x'j = xt + Δxj
            state_deltas: (N, B, state_dim) - Δxj
        """
        # Encode history
        ht = self.encode_history(state_history)

        # Predict deltas
        state_deltas = self.predict_state_deltas(ht, candidate_actions)

        # Apply deltas: x'j = xt + Δxj
        current_state_expanded = current_state.unsqueeze(0).expand(state_deltas.shape[0], -1, -1)
        predicted_states = current_state_expanded + state_deltas

        return predicted_states, state_deltas


class ActionSampler:
    """
    Generates candidate actions for trajectory prediction.
    Actions are (speed_change, heading_change) pairs.
    """

    def __init__(self,
                 speed_range: Tuple[float, float] = (-2.0, 2.0),
                 heading_range: Tuple[float, float] = (-np.pi/4, np.pi/4),
                 num_speed_bins: int = 5,
                 num_heading_bins: int = 5):
        self.speed_range = speed_range
        self.heading_range = heading_range
        self.num_speed_bins = num_speed_bins
        self.num_heading_bins = num_heading_bins

        # Pre-generate candidate actions
        self.candidate_actions = self._generate_candidates()

    def _generate_candidates(self) -> torch.Tensor:
        """Generate discrete candidate actions."""
        speed_values = np.linspace(self.speed_range[0], self.speed_range[1], self.num_speed_bins)
        heading_values = np.linspace(self.heading_range[0], self.heading_range[1], self.num_heading_bins)

        actions = []
        for speed in speed_values:
            for heading in heading_values:
                actions.append([speed, heading])

        return torch.tensor(actions, dtype=torch.float32)

    def get_candidates(self, device: str = 'cpu') -> torch.Tensor:
        """Get candidate actions on specified device."""
        return self.candidate_actions.to(device)

    @property
    def num_actions(self) -> int:
        """Number of candidate actions."""
        return len(self.candidate_actions)


def parse_state_from_data(ped_pos: List[float],
                         ped_speed: float,
                         av_pos: List[float],
                         av_speed: float,
                         av_accel: float = 0.0,
                         dt: float = 0.05) -> torch.Tensor:
    """
    Parse state vector from VR data format.

    Args:
        ped_pos: [x, y] pedestrian position
        ped_speed: pedestrian speed (scalar)
        av_pos: [x, y] AV position
        av_speed: AV speed (scalar)
        av_accel: AV acceleration
        dt: time step for velocity estimation

    Returns:
        state: (8,) tensor [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]
    """
    # Estimate pedestrian velocity components (assuming random direction for now)
    # In practice, you'd compute this from position history
    ped_vx = ped_speed * 0.7  # Placeholder - should be computed from trajectory
    ped_vy = ped_speed * 0.7  # Placeholder - should be computed from trajectory

    state = torch.tensor([
        ped_pos[0], ped_pos[1],  # pedestrian position
        ped_vx, ped_vy,          # pedestrian velocity
        av_pos[0], av_pos[1],    # AV position
        av_speed,                # AV speed
        av_accel                 # AV acceleration
    ], dtype=torch.float32)

    return state


if __name__ == "__main__":
    # Test the model
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # Initialize model and action sampler
    model = LSTMTrajectoryPredictor().to(device)
    action_sampler = ActionSampler()

    # Test data
    batch_size = 4
    seq_length = 10
    state_dim = 8

    # Random test data
    state_history = torch.randn(batch_size, seq_length, state_dim).to(device)
    current_state = torch.randn(batch_size, state_dim).to(device)
    candidate_actions = action_sampler.get_candidates(device)

    print(f"Model initialized on {device}")
    print(f"State history shape: {state_history.shape}")
    print(f"Current state shape: {current_state.shape}")
    print(f"Candidate actions shape: {candidate_actions.shape}")
    print(f"Number of candidate actions: {action_sampler.num_actions}")

    # Forward pass
    predicted_states, state_deltas = model(state_history, candidate_actions, current_state)

    print(f"Predicted states shape: {predicted_states.shape}")
    print(f"State deltas shape: {state_deltas.shape}")
    print("Model test completed successfully!")