#!/usr/bin/env python3
"""
Debug script for VR data loading issues.
"""

import torch
import numpy as np
import pandas as pd
from vr_data_loader import VRTrajectoryDataset, get_dataset_info
import traceback

def debug_single_file():
    """Debug loading a single CSV file."""
    print("🔍 Debugging single file loading")
    print("=" * 40)
    
    try:
        # Load a single file
        csv_file = 'processed_data/Paricipant1_1_processed.csv'
        df = pd.read_csv(csv_file)
        
        print(f"File: {csv_file}")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print(f"First few rows:")
        print(df.head())
        
        # Check for missing values
        print(f"\nMissing values:")
        print(df.isnull().sum())
        
        # Check data types
        print(f"\nData types:")
        print(df.dtypes)
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading single file: {e}")
        traceback.print_exc()
        return False

def debug_dataset_creation():
    """Debug dataset creation."""
    print("\n🔍 Debugging dataset creation")
    print("=" * 40)
    
    try:
        # Create dataset with minimal parameters
        dataset = VRTrajectoryDataset(
            data_path='processed_data',
            obs_len=5,  # Smaller for testing
            pred_len=5,
            normalize=False,  # Disable normalization for debugging
            min_sequence_length=15  # Smaller minimum
        )
        
        print(f"✅ Dataset created successfully")
        print(f"Total samples: {len(dataset)}")
        
        if len(dataset) > 0:
            # Try to get first sample
            sample = dataset[0]
            obs, pred, metadata = sample
            
            print(f"Sample shapes:")
            print(f"  Obs: {obs.shape}")
            print(f"  Pred: {pred.shape}")
            print(f"  Metadata keys: {list(metadata.keys())}")
            
            print(f"Sample values:")
            print(f"  Obs min/max: {obs.min():.3f} / {obs.max():.3f}")
            print(f"  Pred min/max: {pred.min():.3f} / {pred.max():.3f}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        traceback.print_exc()
        return False

def debug_dataloader():
    """Debug dataloader creation."""
    print("\n🔍 Debugging dataloader")
    print("=" * 40)
    
    try:
        from vr_data_loader import create_vr_dataloaders
        
        # Create dataloaders with minimal settings
        train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
            data_path='processed_data',
            batch_size=2,  # Very small batch
            obs_len=5,
            pred_len=5,
            train_split=0.8,
            val_split=0.1,
            normalize=False,  # Disable normalization
            num_workers=0  # No multiprocessing
        )
        
        print(f"✅ Dataloaders created successfully")
        print(f"Train batches: {len(train_loader)}")
        print(f"Val batches: {len(val_loader)}")
        print(f"Test batches: {len(test_loader)}")
        
        # Try to get one batch
        if len(train_loader) > 0:
            batch = next(iter(train_loader))
            obs_batch, pred_batch, metadata_batch = batch
            
            print(f"Batch shapes:")
            print(f"  Obs batch: {obs_batch.shape}")
            print(f"  Pred batch: {pred_batch.shape}")
            print(f"  Metadata count: {len(metadata_batch)}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error with dataloader: {e}")
        traceback.print_exc()
        return False

def debug_state_extraction():
    """Debug state extraction from a single file."""
    print("\n🔍 Debugging state extraction")
    print("=" * 40)
    
    try:
        # Load a single file and extract states manually
        csv_file = 'processed_data/Paricipant1_1_processed.csv'
        df = pd.read_csv(csv_file)
        
        print(f"Processing {len(df)} rows")
        
        # Create a minimal dataset to test state extraction
        dataset = VRTrajectoryDataset(
            data_path='processed_data',
            obs_len=5,
            pred_len=5,
            normalize=False,
            min_sequence_length=15
        )
        
        # Extract states manually
        states = dataset._extract_states(df)
        
        print(f"✅ Extracted {len(states)} states")
        print(f"State shape: {states.shape}")
        print(f"State sample:")
        print(states[:3])  # First 3 states
        
        # Check for any issues
        if np.any(np.isnan(states)):
            print("⚠️  Warning: NaN values found in states")
        if np.any(np.isinf(states)):
            print("⚠️  Warning: Infinite values found in states")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in state extraction: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests."""
    print("🚀 VR Data Loading Debug")
    print("=" * 50)
    
    tests = [
        debug_single_file,
        debug_state_extraction,
        debug_dataset_creation,
        debug_dataloader
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n📋 Debug Summary")
    print("=" * 50)
    
    test_names = [
        "Single file loading",
        "State extraction", 
        "Dataset creation",
        "Dataloader creation"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    if all(results):
        print("\n🎉 All debug tests passed!")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
