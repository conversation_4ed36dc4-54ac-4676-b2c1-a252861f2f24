# Import libraries
import torch
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Device configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'🚀 Using device: {device}')

# Import our modules
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel
from trajectory_inference import create_inference_system
from trajectory_visualization import TrajectoryVisualizer

# Initialize models
trajectory_predictor = LSTMTrajectoryPredictor().to(device)
risk_model = EnhancedRiskModel().to(device)
action_sampler = ActionSampler()
choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)

print(f'Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}')
print(f'Candidate actions: {action_sampler.num_actions}')

# 🔍 DISPLAY RISK MODEL PARAMETERS
print('\n🔍 RISK MODEL PARAMETERS')
print('=' * 40)
print('\nRisk Component Weights (p parameters):')
print(f'  Collision weight:     {risk_model.w_collision.item():.4f}')
print(f'  Path deviation weight: {risk_model.w_path_dev.item():.4f}')
print(f'  Behavioral weight:    {risk_model.w_behavioral.item():.4f}')
print(f'  Perceptual weight:    {risk_model.w_perceptual.item():.4f}')

print('\nAction Cost Weights (q parameters):')
print(f'  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}')
print(f'  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}')

print('\nRisk Function Parameters:')
print(f'  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}')
print(f'  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}')
print(f'  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}')
print(f'  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}')

# Visualize risk parameters
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Risk component weights
weights = [
    risk_model.w_collision.item(),
    risk_model.w_path_dev.item(),
    risk_model.w_behavioral.item(),
    risk_model.w_perceptual.item()
]
labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

bars = axes[0].bar(labels, weights, color=colors, alpha=0.7)
axes[0].set_ylabel('Weight Value')
axes[0].set_title('Risk Component Weights (p)')
axes[0].grid(True, alpha=0.3)

# Add value labels on bars
for bar, weight in zip(bars, weights):
    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                 f'{weight:.3f}', ha='center', va='bottom')

# Action cost weights
cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]
cost_labels = ['Speed Cost', 'Heading Cost']
cost_colors = ['#FFA502', '#FF6348']

bars2 = axes[1].bar(cost_labels, cost_weights, color=cost_colors, alpha=0.7)
axes[1].set_ylabel('Weight Value')
axes[1].set_title('Action Cost Weights (q)')
axes[1].grid(True, alpha=0.3)

for bar, weight in zip(bars2, cost_weights):
    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                 f'{weight:.3f}', ha='center', va='bottom')

plt.tight_layout()
plt.show()

# Create sample trajectory
def create_sample_trajectory(seq_length=30):
    states = []
    ped_pos = np.array([5.0, 5.0])
    ped_vel = np.array([1.0, 0.5])
    av_pos = np.array([25.0, 10.0])
    av_speed = 8.0
    av_accel = 0.0
    
    for t in range(seq_length):
        state = np.array([
            ped_pos[0], ped_pos[1], ped_vel[0], ped_vel[1],
            av_pos[0], av_pos[1], av_speed, av_accel
        ])
        states.append(state)
        
        # Simple physics update
        dt = 0.1
        ped_pos += ped_vel * dt
        av_pos[0] -= av_speed * dt
    
    return np.array(states)

sample_states = create_sample_trajectory()
print(f'Created trajectory: {sample_states.shape}')
print(f'State: [ped_x, ped_y, ped_vx, ped_vy, AV_x, AV_y, AV_v, AV_a]')

# Test single-step prediction
states_tensor = torch.tensor(sample_states, dtype=torch.float32).to(device)
state_history = states_tensor[10:20].unsqueeze(0)
current_state = states_tensor[20].unsqueeze(0)
candidate_actions = action_sampler.get_candidates(device)

with torch.no_grad():
    probabilities, scores = choice_model(state_history, current_state, candidate_actions)
    selected_idx = torch.argmax(probabilities[:, 0]).item()

print(f'\n🎯 Single-step prediction test:')
print(f'  Selected action: {candidate_actions[selected_idx].cpu().numpy()}')
print(f'  Probability: {probabilities[selected_idx, 0].item():.4f}')
print(f'  Risk score: {scores[selected_idx, 0].item():.4f}')

# Test multi-step prediction
inference_system = create_inference_system(
    trajectory_predictor, risk_model, action_sampler, horizon=10
)

with torch.no_grad():
    trajectory = inference_system.predict_trajectory(state_history, current_state)

print(f'\n🚀 Multi-step prediction test:')
print(f'  Predicted {trajectory.states.shape[0]} steps')
print(f'  Avg confidence: {trajectory.confidence.mean().item():.4f}')

print(f'\n🎉 All tests passed! System ready for use.')
print(f'\n📋 Summary:')
print(f'  ✅ LSTM trajectory predictor initialized')
print(f'  ✅ Risk model with {len([p for p in risk_model.parameters()])} learnable parameters')
print(f'  ✅ {action_sampler.num_actions} candidate actions')
print(f'  ✅ Single-step and multi-step prediction working')
print(f'  ✅ Risk parameters displayed for transparency')