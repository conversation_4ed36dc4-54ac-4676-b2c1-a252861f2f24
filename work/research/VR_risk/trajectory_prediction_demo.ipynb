{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# LSTM-Based Trajectory Prediction with Risk-Aware Action Selection\n",
    "\n",
    "This notebook demonstrates the complete trajectory prediction system based on the formulation:\n",
    "\n",
    "## Mathematical Formulation\n",
    "\n",
    "### 1. Joint State Definition\n",
    "$$x_t = (x_{ped}(t), x_{AV}(t))$$\n",
    "\n",
    "### 2. LSTM Operator\n",
    "- **Encoder**: LSTM maps $(T \\times \\dim(x)) \\rightarrow h_t$\n",
    "- **Action Embedding**: $\\phi(a_j) \\in \\mathbb{R}^H$\n",
    "- **Decoder**: MLP takes $[h_t; \\phi(a_j)] \\rightarrow \\Delta x_j$\n",
    "- **Prediction**: $x'_j = x_t + \\Delta x_j$\n",
    "\n",
    "### 3. Risk + Cost Scoring\n",
    "$$S_j = R(x'_j; p) + C(a_j; q)$$\n",
    "\n",
    "### 4. Choice Probability\n",
    "$$\\hat{P}(a_j|x_t) = \\frac{\\exp(-S_j)}{\\sum_{k=1}^N \\exp(-S_k)}$$\n",
    "\n",
    "### 5. Training Loss\n",
    "$$L = -\\log \\hat{P}(a_t|x_t)$$"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import torch\n",
    "import torch.nn as nn\n",
    "import torch.optim as optim\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from pathlib import Path\n",
    "import ast\n",
    "from typing import List, Tuple, Dict, Optional\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set style\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Device configuration\n",
    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "print(f\"Using device: {device}\")\n",
    "\n",
    "# Import our custom modules\n",
    "from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler, parse_state_from_data\n",
    "from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel, cross_entropy_loss\n",
    "from trajectory_inference import TrajectoryInference, TrajectoryEvaluator, create_inference_system\n",
    "from trajectory_visualization import TrajectoryVisualizer, create_comprehensive_report"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Data Loading and Preprocessing\n",
    "\n",
    "Load and preprocess the VR pedestrian crossing data."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_vr_data(data_dir: str, max_files: int = 10) -> List[Dict]:\n",
    "    \"\"\"\n",
    "    Load VR data from CSV files and convert to trajectory format.\n",
    "    \n",
    "    Args:\n",
    "        data_dir: Directory containing processed CSV files\n",
    "        max_files: Maximum number of files to load\n",
    "        \n",
    "    Returns:\n",
    "        List of trajectory dictionaries\n",
    "    \"\"\"\n",
    "    data_path = Path(data_dir)\n",
    "    csv_files = list(data_path.glob(\"*.csv\"))[:max_files]\n",
    "    \n",
    "    trajectories = []\n",
    "    \n",
    "    for csv_file in csv_files:\n",
    "        try:\n",
    "            df = pd.read_csv(csv_file)\n",
    "            \n",
    "            # Extract relevant columns\n",
    "            states = []\n",
    "            actions = []\n",
    "            \n",
    "            for i, row in df.iterrows():\n",
    "                # Parse pedestrian position\n",
    "                ped_pos = ast.literal_eval(row['ped_position'])\n",
    "                ped_speed = row['ped_speed']\n",
    "                \n",
    "                # Parse AV position\n",
    "                av_pos = ast.literal_eval(row['AV_pos'])\n",
    "                av_speed = row['AV_speed']\n",
    "                \n",
    "                # Create state vector\n",
    "                state = parse_state_from_data(\n",
    "                    ped_pos=ped_pos,\n",
    "                    ped_speed=ped_speed,\n",
    "                    av_pos=av_pos,\n",
    "                    av_speed=av_speed\n",
    "                )\n",
    "                \n",
    "                states.append(state.numpy())\n",
    "                \n",
    "                # Compute action (speed and heading change)\n",
    "                if i > 0:\n",
    "                    prev_speed = np.linalg.norm(states[i-1][2:4])\n",
    "                    curr_speed = np.linalg.norm(state[2:4].numpy())\n",
    "                    speed_change = curr_speed - prev_speed\n",
    "                    \n",
    "                    # Simplified heading change (would need proper computation)\n",
    "                    heading_change = np.random.normal(0, 0.1)  # Placeholder\n",
    "                    \n",
    "                    actions.append([speed_change, heading_change])\n",
    "            \n",
    "            if len(states) > 10:  # Minimum sequence length\n",
    "                trajectories.append({\n",
    "                    'states': np.array(states),\n",
    "                    'actions': np.array(actions),\n",
    "                    'file': csv_file.name\n",
    "                })\n",
    "                \n",
    "        except Exception as e:\n",
    "            print(f\"Error processing {csv_file}: {e}\")\n",
    "            continue\n",
    "    \n",
    "    print(f\"Loaded {len(trajectories)} trajectories from {len(csv_files)} files\")\n",
    "    return trajectories\n",
    "\n",
    "# Load data\n",
    "data_dir = \"processed_data\"\n",
    "trajectories = load_vr_data(data_dir, max_files=20)\n",
    "\n",
    "# Display data statistics\n",
    "if trajectories:\n",
    "    lengths = [len(traj['states']) for traj in trajectories]\n",
    "    print(f\"\\nTrajectory Statistics:\")\n",
    "    print(f\"  Number of trajectories: {len(trajectories)}\")\n",
    "    print(f\"  Average length: {np.mean(lengths):.1f} steps\")\n",
    "    print(f\"  Length range: {min(lengths)} - {max(lengths)} steps\")\n",
    "    \n",
    "    # Show sample trajectory\n",
    "    sample_traj = trajectories[0]\n",
    "    print(f\"\\nSample trajectory shape: {sample_traj['states'].shape}\")\n",
    "    print(f\"Sample state: {sample_traj['states'][0]}\")\nelse:\n",
    "    print(\"No trajectories loaded. Creating synthetic data for demonstration.\")"
   ]
  }"
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}