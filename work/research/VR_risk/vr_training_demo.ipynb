# Import libraries
import torch
import numpy as np
import matplotlib.pyplot as plt
import warnings
import time
import os
warnings.filterwarnings('ignore')

# Device configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'🚀 Using device: {device}')

# Import our modules
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel
from trajectory_inference import create_inference_system
from trajectory_visualization import TrajectoryVisualizer
from vr_data_loader import create_vr_dataloaders, get_dataset_info
from vr_training import VRTrajectoryTrainer
from vr_evaluation import VRTrajectoryEvaluator

# Load VR dataset
data_path = 'processed_data'

# Get dataset information
print('📊 ANALYZING VR DATASET')
print('=' * 40)
dataset_info = get_dataset_info(data_path)

print(f'Total samples: {dataset_info["total_samples"]:,}')
print(f'Number of subjects: {dataset_info["num_subjects"]}')
print(f'Number of trials: {dataset_info["num_trials"]}')
print(f'Crossings: {dataset_info["crossings"]:,} ({dataset_info["crossings"]/dataset_info["total_samples"]*100:.1f}%)')
print(f'Non-crossings: {dataset_info["non_crossings"]:,} ({dataset_info["non_crossings"]/dataset_info["total_samples"]*100:.1f}%)')

print('\nBrake behaviors:')
for behavior, count in dataset_info['brake_behaviors'].items():
    percentage = count / dataset_info['total_samples'] * 100
    print(f'  {behavior}: {count:,} ({percentage:.1f}%)')

# Visualize dataset statistics
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Crossing vs non-crossing
crossing_data = [dataset_info['crossings'], dataset_info['non_crossings']]
crossing_labels = ['Crossings', 'Non-crossings']
colors = ['#FF6B6B', '#4ECDC4']

axes[0].pie(crossing_data, labels=crossing_labels, colors=colors, autopct='%1.1f%%', startangle=90)
axes[0].set_title('Crossing Behavior Distribution')

# Brake behaviors
brake_behaviors = list(dataset_info['brake_behaviors'].keys())
brake_counts = list(dataset_info['brake_behaviors'].values())

axes[1].bar(brake_behaviors, brake_counts, color=['#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3'])
axes[1].set_ylabel('Count')
axes[1].set_title('Brake Behavior Distribution')
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Create data loaders
print('🔄 CREATING DATA LOADERS')
print('=' * 40)
train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
    data_path=data_path,
    batch_size=16,  # Smaller batch size
    obs_len=10,
    pred_len=10,
    train_split=0.7,
    val_split=0.15,
    normalize=True,
    num_workers=0  # Disable multiprocessing to avoid tensor issues
)

print(f'✅ Data loaders created successfully!')
print(f'Normalization stats:')
print(f'  Mean: {norm_stats["mean"]}')
print(f'  Std: {norm_stats["std"]}')

# Test data loading
print('\n🧪 TESTING DATA LOADING')
print('=' * 40)
sample_batch = next(iter(train_loader))
obs_seq, pred_seq, metadata = sample_batch

print(f'Batch shapes:')
print(f'  Observation sequence: {obs_seq.shape}')
print(f'  Prediction sequence: {pred_seq.shape}')
print(f'  Metadata samples: {len(metadata)}')

print(f'\nSample metadata:')
sample_meta = metadata[0]
print(f'  Subject: {sample_meta["subject_id"]}')
print(f'  Trial: {sample_meta["trial_id"]}')
print(f'  Crossing: {sample_meta["crossing"]}')
print(f'  Brake behavior: {sample_meta["brake_behavior"]}')
print(f'  CIT: {sample_meta["CIT"]:.3f}')

print(f'\n✅ VR dataset loaded and ready for training!')

# Initialize models
trajectory_predictor = LSTMTrajectoryPredictor().to(device)
risk_model = EnhancedRiskModel().to(device)
action_sampler = ActionSampler()
choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)

print('✅ Models initialized!')
print(f'Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}')
print(f'Candidate actions: {action_sampler.num_actions}')

# 🔍 DISPLAY RISK MODEL PARAMETERS
print('\n🔍 RISK MODEL PARAMETERS')
print('=' * 40)
print('\nRisk Component Weights (p parameters):')
print(f'  Collision weight:     {risk_model.w_collision.item():.4f}')
print(f'  Path deviation weight: {risk_model.w_path_dev.item():.4f}')
print(f'  Behavioral weight:    {risk_model.w_behavioral.item():.4f}')
print(f'  Perceptual weight:    {risk_model.w_perceptual.item():.4f}')

print('\nAction Cost Weights (q parameters):')
print(f'  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}')
print(f'  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}')

print('\nRisk Function Parameters:')
print(f'  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}')
print(f'  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}')
print(f'  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}')
print(f'  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}')

# Visualize risk parameters
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Risk component weights
weights = [
    risk_model.w_collision.item(),
    risk_model.w_path_dev.item(),
    risk_model.w_behavioral.item(),
    risk_model.w_perceptual.item()
]
labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

bars = axes[0].bar(labels, weights, color=colors, alpha=0.7)
axes[0].set_ylabel('Weight Value')
axes[0].set_title('Risk Component Weights (p)')
axes[0].grid(True, alpha=0.3)

# Add value labels on bars
for bar, weight in zip(bars, weights):
    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                 f'{weight:.3f}', ha='center', va='bottom')

# Action cost weights
cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]
cost_labels = ['Speed Cost', 'Heading Cost']
cost_colors = ['#FFA502', '#FF6348']

bars2 = axes[1].bar(cost_labels, cost_weights, color=cost_colors, alpha=0.7)
axes[1].set_ylabel('Weight Value')
axes[1].set_title('Action Cost Weights (q)')
axes[1].grid(True, alpha=0.3)

for bar, weight in zip(bars2, cost_weights):
    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                 f'{weight:.3f}', ha='center', va='bottom')

plt.tight_layout()
plt.show()

# Initialize trainer
trainer = VRTrajectoryTrainer(
    model=choice_model,
    device=device,
    learning_rate=1e-3,
    weight_decay=1e-5
)

print('🚀 TRAINING ON VR DATA')
print('=' * 40)
print(f'Training samples: {len(train_loader.dataset)}')
print(f'Validation samples: {len(val_loader.dataset)}')
print(f'Test samples: {len(test_loader.dataset)}')
print(f'Learning rate: {trainer.learning_rate}')
print(f'Weight decay: {trainer.weight_decay}')

# Train the model
start_time = time.time()
training_history = trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=20,  # Reduced for demo
    save_dir='checkpoints'
)
training_time = time.time() - start_time

print(f'\n✅ Training completed in {training_time/60:.1f} minutes')
print(f'Final training loss: {training_history["train_losses"][-1]:.4f}')
print(f'Final validation loss: {training_history["val_losses"][-1]:.4f}')
print(f'Final training accuracy: {training_history["train_accuracies"][-1]:.4f}')
print(f'Final validation accuracy: {training_history["val_accuracies"][-1]:.4f}')

# Plot training history
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Loss curves
epochs = range(1, len(training_history['train_losses']) + 1)
axes[0].plot(epochs, training_history['train_losses'], 'b-', label='Training Loss', alpha=0.8)
axes[0].plot(epochs, training_history['val_losses'], 'r-', label='Validation Loss', alpha=0.8)
axes[0].set_xlabel('Epoch')
axes[0].set_ylabel('Loss')
axes[0].set_title('Training and Validation Loss')
axes[0].legend()
axes[0].grid(True, alpha=0.3)

# Accuracy curves
axes[1].plot(epochs, training_history['train_accuracies'], 'b-', label='Training Accuracy', alpha=0.8)
axes[1].plot(epochs, training_history['val_accuracies'], 'r-', label='Validation Accuracy', alpha=0.8)
axes[1].set_xlabel('Epoch')
axes[1].set_ylabel('Accuracy')
axes[1].set_title('Training and Validation Accuracy')
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Print training summary
print('📈 TRAINING SUMMARY')
print('=' * 40)
print(f'Best validation loss: {min(training_history["val_losses"]):.4f}')
print(f'Best validation accuracy: {max(training_history["val_accuracies"]):.4f}')
print(f'Training epochs: {len(training_history["train_losses"])}')
print(f'Model saved to: checkpoints/best_model.pth')

# Load best model for evaluation
if os.path.exists('checkpoints/best_model.pth'):
    checkpoint = torch.load('checkpoints/best_model.pth', map_location=device)
    choice_model.load_state_dict(checkpoint['model_state_dict'])
    print(f'✅ Loaded best model from epoch {checkpoint["epoch"] + 1}')

# Initialize evaluator
evaluator = VRTrajectoryEvaluator(
    model=choice_model,
    device=device,
    normalization_stats=norm_stats
)

print('\n🔍 EVALUATING ON TEST SET')
print('=' * 40)

# Compute evaluation metrics
start_time = time.time()
evaluation_results = evaluator.compute_metrics(test_loader)
eval_time = time.time() - start_time

metrics = evaluation_results['metrics']
print(f'Evaluation completed in {eval_time:.1f} seconds')
print(f'\nOverall Metrics:')
print(f'  Position MAE: {metrics["position_mae"]:.3f} m')
print(f'  Position RMSE: {metrics["position_rmse"]:.3f} m')
print(f'  Velocity MAE: {metrics["velocity_mae"]:.3f} m/s')
print(f'  Velocity RMSE: {metrics["velocity_rmse"]:.3f} m/s')
print(f'  Final Displacement Error: {metrics["final_displacement_error"]:.3f} m')
print(f'  Average Displacement Error: {metrics["average_displacement_error"]:.3f} m')

# Analyze by scenario
scenario_metrics = evaluator.analyze_by_scenario(evaluation_results)
print(f'\nScenario Analysis:')
if 'crossing' in scenario_metrics:
    print(f'  Crossing scenarios:')
    print(f'    Position MAE: {scenario_metrics["crossing"]["position_mae"]:.3f} m')
    print(f'    Count: {scenario_metrics["crossing"]["count"]}')
if 'non_crossing' in scenario_metrics:
    print(f'  Non-crossing scenarios:')
    print(f'    Position MAE: {scenario_metrics["non_crossing"]["position_mae"]:.3f} m')
    print(f'    Count: {scenario_metrics["non_crossing"]["count"]}')

# Create evaluation plots
print('📊 CREATING EVALUATION PLOTS')
print('=' * 40)
evaluator.plot_results(evaluation_results, save_dir='evaluation_plots')
print('✅ Evaluation plots saved to evaluation_plots/')

# Display updated risk parameters after training
print('🔍 UPDATED RISK MODEL PARAMETERS AFTER TRAINING')
print('=' * 50)
print('\nRisk Component Weights (p parameters):')
print(f'  Collision weight:     {risk_model.w_collision.item():.4f}')
print(f'  Path deviation weight: {risk_model.w_path_dev.item():.4f}')
print(f'  Behavioral weight:    {risk_model.w_behavioral.item():.4f}')
print(f'  Perceptual weight:    {risk_model.w_perceptual.item():.4f}')

print('\nAction Cost Weights (q parameters):')
print(f'  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}')
print(f'  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}')

print('\nRisk Function Parameters:')
print(f'  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}')
print(f'  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}')
print(f'  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}')
print(f'  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}')

# Visualize updated parameters
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Updated risk component weights
updated_weights = [
    risk_model.w_collision.item(),
    risk_model.w_path_dev.item(),
    risk_model.w_behavioral.item(),
    risk_model.w_perceptual.item()
]
labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

bars = axes[0].bar(labels, updated_weights, color=colors, alpha=0.7)
axes[0].set_ylabel('Weight Value')
axes[0].set_title('Updated Risk Component Weights (p) After Training')
axes[0].grid(True, alpha=0.3)

for bar, weight in zip(bars, updated_weights):
    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                 f'{weight:.3f}', ha='center', va='bottom')

# Updated action cost weights
updated_cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]
cost_labels = ['Speed Cost', 'Heading Cost']
cost_colors = ['#FFA502', '#FF6348']

bars2 = axes[1].bar(cost_labels, updated_cost_weights, color=cost_colors, alpha=0.7)
axes[1].set_ylabel('Weight Value')
axes[1].set_title('Updated Action Cost Weights (q) After Training')
axes[1].grid(True, alpha=0.3)

for bar, weight in zip(bars2, updated_cost_weights):
    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                 f'{weight:.3f}', ha='center', va='bottom')

plt.tight_layout()
plt.show()

# Import multi-step trajectory generation
from multi_step_trajectory_generator import MultiStepTrajectoryGenerator
from multi_step_visualization import MultiStepTrajectoryVisualizer

print('🚀 MULTI-STEP TRAJECTORY PREDICTION')
print('=' * 50)
print('💡 Using your ALREADY TRAINED models - no additional training needed!')

# Initialize multi-step generator (uses trained models)
multi_step_generator = MultiStepTrajectoryGenerator(
    trajectory_predictor=trajectory_predictor,  # ✅ Already trained
    risk_model=risk_model,                      # ✅ Already trained
    action_sampler=action_sampler,              # ✅ No training needed
    horizon=10,  # Predict 10 steps ahead
    num_trajectories=20,  # Generate 20 candidate trajectories
    sampling_strategy='top_k'  # Sample from top-k actions
)

# Initialize visualizer
visualizer = MultiStepTrajectoryVisualizer()

print(f'✅ Multi-step generator initialized (no training required!)')
print(f'  Horizon: {multi_step_generator.horizon} steps')
print(f'  Candidate trajectories: {multi_step_generator.num_trajectories}')
print(f'  Sampling strategy: {multi_step_generator.sampling_strategy}')
print(f'  Status: Ready to use immediately! 🎉')

# Test multi-step prediction on a sample
print('🧪 TESTING MULTI-STEP PREDICTION')
print('=' * 40)

# Get a test sample
test_batch = next(iter(test_loader))
obs_seq, pred_seq, metadata = test_batch
obs_seq = obs_seq.to(device)
pred_seq = pred_seq.to(device)

# Use first sample from batch
sample_obs = obs_seq[0:1]  # [1, T, state_dim]
sample_current = obs_seq[0, -1:].unsqueeze(0)  # [1, state_dim]

print(f'Input shapes:')
print(f'  Observation history: {sample_obs.shape}')
print(f'  Current state: {sample_current.shape}')

# Generate multiple trajectories
with torch.no_grad():
    results = multi_step_generator.predict_pedestrian_trajectory(
        state_history=sample_obs,
        current_state=sample_current,
        num_samples=15
    )

print(f'\n📊 TRAJECTORY PREDICTION RESULTS')
print(f'=' * 40)
print(f'Best trajectory total risk: {results["best_trajectory"]["total_risk"]:.4f}')
print(f'Risk statistics:')
print(f'  Min risk: {results["risk_analysis"]["min_risk"]:.4f}')
print(f'  Mean risk: {results["risk_analysis"]["mean_risk"]:.4f}')
print(f'  Max risk: {results["risk_analysis"]["max_risk"]:.4f}')
print(f'  Risk std: {results["risk_analysis"]["std_risk"]:.4f}')
print(f'Number of trajectory candidates: {results["trajectory_diversity"]["num_candidates"]}')
print(f'Risk range: {results["trajectory_diversity"]["risk_range"]:.4f}')

# Visualize the multi-step trajectory prediction
print('📊 VISUALIZING MULTI-STEP TRAJECTORIES')
print('=' * 40)

# Create comprehensive risk analysis plot
visualizer.plot_risk_analysis(results, save_path='multi_step_risk_analysis.png')

print('✅ Multi-step trajectory visualization complete!')
print('\n🎯 KEY INSIGHTS:')
print('1. The system generates multiple complete 10-step trajectories')
print('2. Each trajectory is evaluated for total risk')
print('3. The trajectory with minimum risk is selected')
print('4. Risk varies significantly across different trajectory candidates')
print('5. The selected trajectory balances safety and realism')