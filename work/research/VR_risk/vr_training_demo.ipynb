{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LSTM-Based Trajectory Prediction with VR Data Training\n", "\n", "This notebook demonstrates the complete trajectory prediction system using **real VR pedestrian crossing data** for training and evaluation.\n", "\n", "## Mathematical Formulation\n", "\n", "1. **Joint State**: x_t = (x_ped(t), x_AV(t))\n", "2. **LSTM Operator**: History → Encoder → Action Embedding → Decoder → State Delta\n", "3. **Risk + Cost**: S_j = R(x'_j; p) + C(a_j; q)\n", "4. **Choice Probability**: P^(a_j|x_t) = exp(-S_j) / Σ_k exp(-S_k)\n", "5. **Training Loss**: L = -log P^(a_t|x_t)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Using device: cpu\n"]}], "source": ["# Import libraries\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "import time\n", "import os\n", "warnings.filterwarnings('ignore')\n", "\n", "# Device configuration\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f'🚀 Using device: {device}')\n", "\n", "# Import our modules\n", "from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler\n", "from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel\n", "from trajectory_inference import create_inference_system\n", "from trajectory_visualization import TrajectoryVisualizer\n", "from vr_data_loader import create_vr_dataloaders, get_dataset_info\n", "from vr_training import VRTrajectoryTrainer\n", "from vr_evaluation import VRTrajectoryEvaluator"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and Analyze VR Dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 ANALYZING VR DATASET\n", "========================================\n", "Found 414 CSV files\n", "Loaded 414 total sequences, 414 valid sequences\n", "Created 134481 sliding window samples\n", "Total samples: 134,481\n", "Number of subjects: 30\n", "Number of trials: 414\n", "Crossings: 220 (0.2%)\n", "Non-crossings: 134,261 (99.8%)\n", "\n", "Brake behaviors:\n", "  BRAKE_2STAGES_STRONG_BRAKE_2ND_EARLYSTOP: 8,976 (6.7%)\n", "  BRAKE_2STAGES_STRONG_BRAKE_1ST_EARLYSTOP: 9,245 (6.9%)\n", "  BRAKE_2STAGES_STRONG_BRAKE_2ND_MEDIUMSTOP: 8,866 (6.6%)\n", "  AV_DRIVES_THROUGH: 14,973 (11.1%)\n", "  BRAKE_2STAGES_STRONG_BRAKE_1ST_MEDIUMSTOP: 9,144 (6.8%)\n", "  BRAKE_2STAGES_STRONG_BRAKE_1ST_LATESTOP: 10,758 (8.0%)\n", "  BRAKE_LINEARLY_LATESTOP: 18,856 (14.0%)\n", "  BRAKE_2STAGES_STRONG_BRAKE_2ND_LATESTOP: 9,289 (6.9%)\n", "  BRAKE_LINEARLY_EARLYSTOP: 17,746 (13.2%)\n", "  BRAKE_EMERGENCY: 9,198 (6.8%)\n", "  BRAKE_LINEARLY_MEDIUMSTOP: 17,430 (13.0%)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load VR dataset\n", "data_path = 'processed_data'\n", "\n", "# Get dataset information\n", "print('📊 ANALYZING VR DATASET')\n", "print('=' * 40)\n", "dataset_info = get_dataset_info(data_path)\n", "\n", "print(f'Total samples: {dataset_info[\"total_samples\"]:,}')\n", "print(f'Number of subjects: {dataset_info[\"num_subjects\"]}')\n", "print(f'Number of trials: {dataset_info[\"num_trials\"]}')\n", "print(f'Crossings: {dataset_info[\"crossings\"]:,} ({dataset_info[\"crossings\"]/dataset_info[\"total_samples\"]*100:.1f}%)')\n", "print(f'Non-crossings: {dataset_info[\"non_crossings\"]:,} ({dataset_info[\"non_crossings\"]/dataset_info[\"total_samples\"]*100:.1f}%)')\n", "\n", "print('\\nBrake behaviors:')\n", "for behavior, count in dataset_info['brake_behaviors'].items():\n", "    percentage = count / dataset_info['total_samples'] * 100\n", "    print(f'  {behavior}: {count:,} ({percentage:.1f}%)')\n", "\n", "# Visualize dataset statistics\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Crossing vs non-crossing\n", "crossing_data = [dataset_info['crossings'], dataset_info['non_crossings']]\n", "crossing_labels = ['Crossings', 'Non-crossings']\n", "colors = ['#FF6B6B', '#4ECDC4']\n", "\n", "axes[0].pie(crossing_data, labels=crossing_labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "axes[0].set_title('Crossing Behavior Distribution')\n", "\n", "# Brake behaviors\n", "brake_behaviors = list(dataset_info['brake_behaviors'].keys())\n", "brake_counts = list(dataset_info['brake_behaviors'].values())\n", "\n", "axes[1].bar(brake_behaviors, brake_counts, color=['#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3'])\n", "axes[1].set_ylabel('Count')\n", "axes[1].set_title('Brake Behavior Distribution')\n", "axes[1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Create Data Loaders"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 CREATING DATA LOADERS\n", "========================================\n", "Found 414 CSV files\n", "Loaded 414 total sequences, 414 valid sequences\n", "Created 134481 sliding window samples\n", "Computed normalization stats:\n", "  Mean: [ 2.8491314e+01  1.4326314e+01 -1.0626707e-03 -3.9049193e-01\n", "  2.4500422e+01  1.5792528e+01  5.7381325e+00 -7.6404721e-03]\n", "  Std: [ 0.25128365  3.036767    0.45916897  1.5611238  25.790543    0.8760885\n", "  3.2571743   5.448686  ]\n", "Dataset splits:\n", "  Train: 94136 samples\n", "  Val: 20172 samples\n", "  Test: 20173 samples\n", "✅ Data loaders created successfully!\n", "Normalization stats:\n", "  Mean: [ 2.8491314e+01  1.4326314e+01 -1.0626707e-03 -3.9049193e-01\n", "  2.4500422e+01  1.5792528e+01  5.7381325e+00 -7.6404721e-03]\n", "  Std: [ 0.25128365  3.036767    0.45916897  1.5611238  25.790543    0.8760885\n", "  3.2571743   5.448686  ]\n", "\n", "🧪 TESTING DATA LOADING\n", "========================================\n"]}, {"ename": "RuntimeError", "evalue": "Caught RuntimeError in DataLoader worker process 0.\nOriginal Traceback (most recent call last):\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/worker.py\", line 351, in _worker_loop\n    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]\n           ^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/fetch.py\", line 55, in fetch\n    return self.collate_fn(data)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 398, in default_collate\n    return collate(batch, collate_fn_map=default_collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 211, in collate\n    return [\n           ^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 212, in <listcomp>\n    collate(samples, collate_fn_map=collate_fn_map)\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 171, in collate\n    {\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 172, in <dictcomp>\n    key: collate(\n         ^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 155, in collate\n    return collate_fn_map[elem_type](batch, collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 285, in collate_numpy_array_fn\n    return collate([torch.as_tensor(b) for b in batch], collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 155, in collate\n    return collate_fn_map[elem_type](batch, collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 271, in collate_tensor_fn\n    out = elem.new(storage).resize_(len(batch), *list(elem.size()))\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nRuntimeError: Trying to resize storage that is not resizable\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 23\u001b[39m\n\u001b[32m     21\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m🧪 TESTING DATA LOADING\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     22\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m'\u001b[39m\u001b[33m=\u001b[39m\u001b[33m'\u001b[39m * \u001b[32m40\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m sample_batch = \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43miter\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     24\u001b[39m obs_seq, pred_seq, metadata = sample_batch\n\u001b[32m     26\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33mBatch shapes:\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/dataloader.py:701\u001b[39m, in \u001b[36m_BaseDataLoaderIter.__next__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    698\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._sampler_iter \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    699\u001b[39m     \u001b[38;5;66;03m# TODO(https://github.com/pytorch/pytorch/issues/76750)\u001b[39;00m\n\u001b[32m    700\u001b[39m     \u001b[38;5;28mself\u001b[39m._reset()  \u001b[38;5;66;03m# type: ignore[call-arg]\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m701\u001b[39m data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_next_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    702\u001b[39m \u001b[38;5;28mself\u001b[39m._num_yielded += \u001b[32m1\u001b[39m\n\u001b[32m    703\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[32m    704\u001b[39m     \u001b[38;5;28mself\u001b[39m._dataset_kind == _DatasetKind.Iterable\n\u001b[32m    705\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._IterableDataset_len_called \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    706\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._num_yielded > \u001b[38;5;28mself\u001b[39m._IterableDataset_len_called\n\u001b[32m    707\u001b[39m ):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/dataloader.py:1465\u001b[39m, in \u001b[36m_MultiProcessingDataLoaderIter._next_data\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1463\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1464\u001b[39m     \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m._task_info[idx]\n\u001b[32m-> \u001b[39m\u001b[32m1465\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_process_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/dataloader.py:1491\u001b[39m, in \u001b[36m_MultiProcessingDataLoaderIter._process_data\u001b[39m\u001b[34m(self, data)\u001b[39m\n\u001b[32m   1489\u001b[39m \u001b[38;5;28mself\u001b[39m._try_put_index()\n\u001b[32m   1490\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, ExceptionWrapper):\n\u001b[32m-> \u001b[39m\u001b[32m1491\u001b[39m     \u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1492\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m data\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/vr/lib/python3.11/site-packages/torch/_utils.py:715\u001b[39m, in \u001b[36mExceptionWrapper.reraise\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    711\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[32m    712\u001b[39m     \u001b[38;5;66;03m# If the exception takes multiple arguments, don't try to\u001b[39;00m\n\u001b[32m    713\u001b[39m     \u001b[38;5;66;03m# instantiate since we don't know how to\u001b[39;00m\n\u001b[32m    714\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(msg) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m715\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exception\n", "\u001b[31mRuntimeError\u001b[39m: Caught RuntimeError in DataLoader worker process 0.\nOriginal Traceback (most recent call last):\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/worker.py\", line 351, in _worker_loop\n    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]\n           ^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/fetch.py\", line 55, in fetch\n    return self.collate_fn(data)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 398, in default_collate\n    return collate(batch, collate_fn_map=default_collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 211, in collate\n    return [\n           ^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 212, in <listcomp>\n    collate(samples, collate_fn_map=collate_fn_map)\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 171, in collate\n    {\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 172, in <dictcomp>\n    key: collate(\n         ^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 155, in collate\n    return collate_fn_map[elem_type](batch, collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 285, in collate_numpy_array_fn\n    return collate([torch.as_tensor(b) for b in batch], collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 155, in collate\n    return collate_fn_map[elem_type](batch, collate_fn_map=collate_fn_map)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/home/<USER>/miniconda3/envs/vr/lib/python3.11/site-packages/torch/utils/data/_utils/collate.py\", line 271, in collate_tensor_fn\n    out = elem.new(storage).resize_(len(batch), *list(elem.size()))\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nRuntimeError: Trying to resize storage that is not resizable\n"]}], "source": ["# Create data loaders\n", "print('🔄 CREATING DATA LOADERS')\n", "print('=' * 40)\n", "train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(\n", "    data_path=data_path,\n", "    batch_size=32,\n", "    obs_len=10,\n", "    pred_len=10,\n", "    train_split=0.7,\n", "    val_split=0.15,\n", "    normalize=True,\n", "    num_workers=2\n", ")\n", "\n", "print(f'✅ Data loaders created successfully!')\n", "print(f'Normalization stats:')\n", "print(f'  Mean: {norm_stats[\"mean\"]}')\n", "print(f'  Std: {norm_stats[\"std\"]}')\n", "\n", "# Test data loading\n", "print('\\n🧪 TESTING DATA LOADING')\n", "print('=' * 40)\n", "sample_batch = next(iter(train_loader))\n", "obs_seq, pred_seq, metadata = sample_batch\n", "\n", "print(f'Batch shapes:')\n", "print(f'  Observation sequence: {obs_seq.shape}')\n", "print(f'  Prediction sequence: {pred_seq.shape}')\n", "print(f'  Metadata samples: {len(metadata)}')\n", "\n", "print(f'\\nSample metadata:')\n", "sample_meta = metadata[0]\n", "print(f'  Subject: {sample_meta[\"subject_id\"]}')\n", "print(f'  Trial: {sample_meta[\"trial_id\"]}')\n", "print(f'  Crossing: {sample_meta[\"crossing\"]}')\n", "print(f'  Brake behavior: {sample_meta[\"brake_behavior\"]}')\n", "print(f'  CIT: {sample_meta[\"CIT\"]:.3f}')\n", "\n", "print(f'\\n✅ VR dataset loaded and ready for training!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Initialize Models and Display Risk Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize models\n", "trajectory_predictor = LSTMTrajectoryPredictor().to(device)\n", "risk_model = EnhancedRiskModel().to(device)\n", "action_sampler = ActionSampler()\n", "choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)\n", "\n", "print('✅ Models initialized!')\n", "print(f'Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}')\n", "print(f'Candidate actions: {action_sampler.num_actions}')\n", "\n", "# 🔍 DISPLAY RISK MODEL PARAMETERS\n", "print('\\n🔍 RISK MODEL PARAMETERS')\n", "print('=' * 40)\n", "print('\\nRisk Component Weights (p parameters):')\n", "print(f'  Collision weight:     {risk_model.w_collision.item():.4f}')\n", "print(f'  Path deviation weight: {risk_model.w_path_dev.item():.4f}')\n", "print(f'  Behavioral weight:    {risk_model.w_behavioral.item():.4f}')\n", "print(f'  Perceptual weight:    {risk_model.w_perceptual.item():.4f}')\n", "\n", "print('\\nAction Cost Weights (q parameters):')\n", "print(f'  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}')\n", "print(f'  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}')\n", "\n", "print('\\nRisk Function Parameters:')\n", "print(f'  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}')\n", "print(f'  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}')\n", "print(f'  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}')\n", "print(f'  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualize Risk Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize risk parameters\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Risk component weights\n", "weights = [\n", "    risk_model.w_collision.item(),\n", "    risk_model.w_path_dev.item(),\n", "    risk_model.w_behavioral.item(),\n", "    risk_model.w_perceptual.item()\n", "]\n", "labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "\n", "bars = axes[0].bar(labels, weights, color=colors, alpha=0.7)\n", "axes[0].set_ylabel('Weight Value')\n", "axes[0].set_title('Risk Component Weights (p)')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, weight in zip(bars, weights):\n", "    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                 f'{weight:.3f}', ha='center', va='bottom')\n", "\n", "# Action cost weights\n", "cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]\n", "cost_labels = ['Speed Cost', 'Heading Cost']\n", "cost_colors = ['#FFA502', '#FF6348']\n", "\n", "bars2 = axes[1].bar(cost_labels, cost_weights, color=cost_colors, alpha=0.7)\n", "axes[1].set_ylabel('Weight Value')\n", "axes[1].set_title('Action Cost Weights (q)')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "for bar, weight in zip(bars2, cost_weights):\n", "    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\n", "                 f'{weight:.3f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train the Model on VR Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize trainer\n", "trainer = VRTrajectoryTrainer(\n", "    model=choice_model,\n", "    device=device,\n", "    learning_rate=1e-3,\n", "    weight_decay=1e-5\n", ")\n", "\n", "print('🚀 TRAINING ON VR DATA')\n", "print('=' * 40)\n", "print(f'Training samples: {len(train_loader.dataset)}')\n", "print(f'Validation samples: {len(val_loader.dataset)}')\n", "print(f'Test samples: {len(test_loader.dataset)}')\n", "print(f'Learning rate: {trainer.learning_rate}')\n", "print(f'Weight decay: {trainer.weight_decay}')\n", "\n", "# Train the model\n", "start_time = time.time()\n", "training_history = trainer.train(\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    num_epochs=20,  # Reduced for demo\n", "    save_dir='checkpoints'\n", ")\n", "training_time = time.time() - start_time\n", "\n", "print(f'\\n✅ Training completed in {training_time/60:.1f} minutes')\n", "print(f'Final training loss: {training_history[\"train_losses\"][-1]:.4f}')\n", "print(f'Final validation loss: {training_history[\"val_losses\"][-1]:.4f}')\n", "print(f'Final training accuracy: {training_history[\"train_accuracies\"][-1]:.4f}')\n", "print(f'Final validation accuracy: {training_history[\"val_accuracies\"][-1]:.4f}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Visualize Training Progress"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Loss curves\n", "epochs = range(1, len(training_history['train_losses']) + 1)\n", "axes[0].plot(epochs, training_history['train_losses'], 'b-', label='Training Loss', alpha=0.8)\n", "axes[0].plot(epochs, training_history['val_losses'], 'r-', label='Validation Loss', alpha=0.8)\n", "axes[0].set_xlabel('Epoch')\n", "axes[0].set_ylabel('Loss')\n", "axes[0].set_title('Training and Validation Loss')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Accuracy curves\n", "axes[1].plot(epochs, training_history['train_accuracies'], 'b-', label='Training Accuracy', alpha=0.8)\n", "axes[1].plot(epochs, training_history['val_accuracies'], 'r-', label='Validation Accuracy', alpha=0.8)\n", "axes[1].set_xlabel('Epoch')\n", "axes[1].set_ylabel('Accuracy')\n", "axes[1].set_title('Training and Validation Accuracy')\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print training summary\n", "print('📈 TRAINING SUMMARY')\n", "print('=' * 40)\n", "print(f'Best validation loss: {min(training_history[\"val_losses\"]):.4f}')\n", "print(f'Best validation accuracy: {max(training_history[\"val_accuracies\"]):.4f}')\n", "print(f'Training epochs: {len(training_history[\"train_losses\"])}')\n", "print(f'Model saved to: checkpoints/best_model.pth')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> on Test Set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load best model for evaluation\n", "if os.path.exists('checkpoints/best_model.pth'):\n", "    checkpoint = torch.load('checkpoints/best_model.pth', map_location=device)\n", "    choice_model.load_state_dict(checkpoint['model_state_dict'])\n", "    print(f'✅ Loaded best model from epoch {checkpoint[\"epoch\"] + 1}')\n", "\n", "# Initialize evaluator\n", "evaluator = VRTrajectoryEvaluator(\n", "    model=choice_model,\n", "    device=device,\n", "    normalization_stats=norm_stats\n", ")\n", "\n", "print('\\n🔍 EVALUATING ON TEST SET')\n", "print('=' * 40)\n", "\n", "# Compute evaluation metrics\n", "start_time = time.time()\n", "evaluation_results = evaluator.compute_metrics(test_loader)\n", "eval_time = time.time() - start_time\n", "\n", "metrics = evaluation_results['metrics']\n", "print(f'Evaluation completed in {eval_time:.1f} seconds')\n", "print(f'\\nOverall Metrics:')\n", "print(f'  Position MAE: {metrics[\"position_mae\"]:.3f} m')\n", "print(f'  Position RMSE: {metrics[\"position_rmse\"]:.3f} m')\n", "print(f'  Velocity MAE: {metrics[\"velocity_mae\"]:.3f} m/s')\n", "print(f'  Velocity RMSE: {metrics[\"velocity_rmse\"]:.3f} m/s')\n", "print(f'  Final Displacement Error: {metrics[\"final_displacement_error\"]:.3f} m')\n", "print(f'  Average Displacement Error: {metrics[\"average_displacement_error\"]:.3f} m')\n", "\n", "# Analyze by scenario\n", "scenario_metrics = evaluator.analyze_by_scenario(evaluation_results)\n", "print(f'\\nScenario Analysis:')\n", "if 'crossing' in scenario_metrics:\n", "    print(f'  Crossing scenarios:')\n", "    print(f'    Position MAE: {scenario_metrics[\"crossing\"][\"position_mae\"]:.3f} m')\n", "    print(f'    Count: {scenario_metrics[\"crossing\"][\"count\"]}')\n", "if 'non_crossing' in scenario_metrics:\n", "    print(f'  Non-crossing scenarios:')\n", "    print(f'    Position MAE: {scenario_metrics[\"non_crossing\"][\"position_mae\"]:.3f} m')\n", "    print(f'    Count: {scenario_metrics[\"non_crossing\"][\"count\"]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Visualize Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create evaluation plots\n", "print('📊 CREATING EVALUATION PLOTS')\n", "print('=' * 40)\n", "evaluator.plot_results(evaluation_results, save_dir='evaluation_plots')\n", "print('✅ Evaluation plots saved to evaluation_plots/')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Updated Risk Parameters After Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display updated risk parameters after training\n", "print('🔍 UPDATED RISK MODEL PARAMETERS AFTER TRAINING')\n", "print('=' * 50)\n", "print('\\nRisk Component Weights (p parameters):')\n", "print(f'  Collision weight:     {risk_model.w_collision.item():.4f}')\n", "print(f'  Path deviation weight: {risk_model.w_path_dev.item():.4f}')\n", "print(f'  Behavioral weight:    {risk_model.w_behavioral.item():.4f}')\n", "print(f'  Perceptual weight:    {risk_model.w_perceptual.item():.4f}')\n", "\n", "print('\\nAction Cost Weights (q parameters):')\n", "print(f'  Speed cost weight:    {risk_model.w_speed_cost.item():.4f}')\n", "print(f'  Heading cost weight:  {risk_model.w_heading_cost.item():.4f}')\n", "\n", "print('\\nRisk Function Parameters:')\n", "print(f'  Collision sharpness:      {risk_model.collision_sharpness.item():.4f}')\n", "print(f'  Path deviation sharpness: {risk_model.path_dev_sharpness.item():.4f}')\n", "print(f'  Behavioral sensitivity:   {risk_model.behavioral_sensitivity.item():.4f}')\n", "print(f'  Perceptual noise scale:   {risk_model.perceptual_noise_scale.item():.4f}')\n", "\n", "# Visualize updated parameters\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Updated risk component weights\n", "updated_weights = [\n", "    risk_model.w_collision.item(),\n", "    risk_model.w_path_dev.item(),\n", "    risk_model.w_behavioral.item(),\n", "    risk_model.w_perceptual.item()\n", "]\n", "labels = ['Collision', 'Path Dev', 'Behavioral', 'Perceptual']\n", "colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "\n", "bars = axes[0].bar(labels, updated_weights, color=colors, alpha=0.7)\n", "axes[0].set_ylabel('Weight Value')\n", "axes[0].set_title('Updated Risk Component Weights (p) After Training')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "for bar, weight in zip(bars, updated_weights):\n", "    axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                 f'{weight:.3f}', ha='center', va='bottom')\n", "\n", "# Updated action cost weights\n", "updated_cost_weights = [risk_model.w_speed_cost.item(), risk_model.w_heading_cost.item()]\n", "cost_labels = ['Speed Cost', 'Heading Cost']\n", "cost_colors = ['#FFA502', '#FF6348']\n", "\n", "bars2 = axes[1].bar(cost_labels, updated_cost_weights, color=cost_colors, alpha=0.7)\n", "axes[1].set_ylabel('Weight Value')\n", "axes[1].set_title('Updated Action Cost Weights (q) After Training')\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "for bar, weight in zip(bars2, updated_cost_weights):\n", "    axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\n", "                 f'{weight:.3f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated:\n", "\n", "✅ **Complete VR data loading and analysis**  \n", "✅ **LSTM-based trajectory prediction system training**  \n", "✅ **Risk model parameters displayed for transparency**  \n", "✅ **Training on real VR pedestrian crossing data**  \n", "✅ **Comprehensive evaluation and visualization**  \n", "✅ **Updated risk parameters after training**  \n", "\n", "### Key Results:\n", "\n", "- **Dataset**: Used real VR data from 30 participants with crossing and non-crossing scenarios\n", "- **Training**: Successfully trained LSTM trajectory predictor with risk-aware action selection\n", "- **Risk Parameters**: All risk model parameters (p and q) are displayed for full transparency\n", "- **Evaluation**: Comprehensive metrics including position/velocity errors and scenario analysis\n", "- **Visualization**: Training curves, evaluation plots, and parameter comparisons\n", "\n", "The system is now trained on your VR data and ready for deployment in practical applications.\n", "\n", "### Next Steps:\n", "1. Fine-tune hyperparameters for better performance\n", "2. Experiment with different risk parameter initializations\n", "3. Add more sophisticated risk components\n", "4. Deploy for real-time trajectory prediction"]}], "metadata": {"kernelspec": {"display_name": "vr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}