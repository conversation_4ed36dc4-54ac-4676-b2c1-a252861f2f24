"""
Visualization for multi-step trajectory prediction with risk analysis.
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from typing import List, Dict, Optional
import torch

from multi_step_trajectory_generator import TrajectoryCandidate, MultiStepTrajectoryGenerator


class MultiStepTrajectoryVisualizer:
    """Visualizer for multi-step trajectory predictions."""
    
    def __init__(self, figsize=(15, 10)):
        self.figsize = figsize
        plt.style.use('default')
        
    def plot_trajectory_candidates(self, 
                                 all_trajectories: List[TrajectoryCandidate],
                                 best_trajectory: TrajectoryCandidate,
                                 current_state: torch.Tensor,
                                 state_history: Optional[torch.Tensor] = None,
                                 save_path: Optional[str] = None):
        """
        Plot multiple trajectory candidates with the best one highlighted.
        
        Args:
            all_trajectories: All generated trajectory candidates
            best_trajectory: Selected best trajectory
            current_state: Current state
            state_history: Historical states for context
            save_path: Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # Extract positions
        current_ped_pos = current_state[:2].cpu().numpy()
        current_av_pos = current_state[4:6].cpu().numpy()
        
        # 1. Trajectory paths (top-left)
        ax = axes[0, 0]
        
        # Plot all trajectory candidates in light colors
        for i, traj in enumerate(all_trajectories):
            ped_positions = traj.states[:, :2].cpu().numpy()
            ax.plot(ped_positions[:, 0], ped_positions[:, 1], 
                   'lightblue', alpha=0.3, linewidth=1, label='Candidates' if i == 0 else '')
        
        # Plot best trajectory in bold
        best_ped_positions = best_trajectory.states[:, :2].cpu().numpy()
        ax.plot(best_ped_positions[:, 0], best_ped_positions[:, 1], 
               'red', linewidth=3, marker='o', markersize=4, label='Best Trajectory')
        
        # Plot current positions
        ax.plot(current_ped_pos[0], current_ped_pos[1], 'go', markersize=10, label='Current Ped')
        ax.plot(current_av_pos[0], current_av_pos[1], 'bs', markersize=10, label='Current AV')
        
        # Plot historical path if available
        if state_history is not None:
            hist_ped_pos = state_history[0, :, :2].cpu().numpy()
            ax.plot(hist_ped_pos[:, 0], hist_ped_pos[:, 1], 
                   'gray', linestyle='--', alpha=0.7, label='History')
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title('Trajectory Candidates')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Risk over time (top-right)
        ax = axes[0, 1]
        
        # Plot risk for all trajectories
        for i, traj in enumerate(all_trajectories):
            step_risks = traj.step_risks.cpu().numpy()
            ax.plot(range(len(step_risks)), step_risks, 
                   'lightcoral', alpha=0.3, linewidth=1)
        
        # Plot best trajectory risk
        best_risks = best_trajectory.step_risks.cpu().numpy()
        ax.plot(range(len(best_risks)), best_risks, 
               'darkred', linewidth=3, marker='o', markersize=4, label='Best Trajectory')
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Risk Score')
        ax.set_title('Risk Evolution Over Time')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 3. Risk distribution (bottom-left)
        ax = axes[1, 0]
        
        total_risks = [traj.total_risk for traj in all_trajectories]
        ax.hist(total_risks, bins=min(10, len(total_risks)), alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(best_trajectory.total_risk, color='red', linestyle='--', linewidth=2, 
                  label=f'Best: {best_trajectory.total_risk:.3f}')
        
        ax.set_xlabel('Total Risk')
        ax.set_ylabel('Frequency')
        ax.set_title('Risk Distribution Across Trajectories')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. Action probabilities (bottom-right)
        ax = axes[1, 1]
        
        best_probs = best_trajectory.probabilities.cpu().numpy()
        ax.plot(range(len(best_probs)), best_probs, 
               'purple', linewidth=2, marker='s', markersize=4)
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Action Probability')
        ax.set_title('Action Probabilities (Best Trajectory)')
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_risk_analysis(self, 
                          results: Dict,
                          save_path: Optional[str] = None):
        """
        Plot comprehensive risk analysis.
        
        Args:
            results: Results from predict_pedestrian_trajectory
            save_path: Path to save the plot
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        
        best_traj = results['best_trajectory']
        risk_analysis = results['risk_analysis']
        
        # 1. Trajectory path with risk coloring (top-left)
        ax = axes[0, 0]
        positions = best_traj['positions']
        step_risks = best_traj['step_risks']
        
        # Create a colormap for risk
        scatter = ax.scatter(positions[:, 0], positions[:, 1], 
                           c=step_risks, cmap='Reds', s=100, alpha=0.8)
        ax.plot(positions[:, 0], positions[:, 1], 'k-', alpha=0.5, linewidth=1)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Risk Score')
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title('Trajectory with Risk Coloring')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Risk over time (top-middle)
        ax = axes[0, 1]
        ax.plot(range(len(step_risks)), step_risks, 'r-', linewidth=2, marker='o')
        ax.fill_between(range(len(step_risks)), step_risks, alpha=0.3, color='red')
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Risk Score')
        ax.set_title('Risk Evolution')
        ax.grid(True, alpha=0.3)
        
        # 3. Speed profile (top-right)
        ax = axes[0, 2]
        states = best_traj['states']
        speeds = np.sqrt(states[:, 2]**2 + states[:, 3]**2)  # Pedestrian speed
        
        ax.plot(range(len(speeds)), speeds, 'b-', linewidth=2, marker='s')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Speed (m/s)')
        ax.set_title('Pedestrian Speed Profile')
        ax.grid(True, alpha=0.3)
        
        # 4. Risk statistics (bottom-left)
        ax = axes[1, 0]
        risk_stats = [
            risk_analysis['min_risk'],
            risk_analysis['mean_risk'],
            risk_analysis['max_risk']
        ]
        labels = ['Min', 'Mean', 'Max']
        colors = ['green', 'orange', 'red']
        
        bars = ax.bar(labels, risk_stats, color=colors, alpha=0.7)
        ax.set_ylabel('Risk Score')
        ax.set_title('Risk Statistics')
        ax.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, risk_stats):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom')
        
        # 5. Action magnitude over time (bottom-middle)
        ax = axes[1, 1]
        actions = best_traj['actions']
        action_magnitudes = np.sqrt(actions[:, 0]**2 + actions[:, 1]**2)
        
        ax.plot(range(len(action_magnitudes)), action_magnitudes, 'g-', linewidth=2, marker='^')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Action Magnitude')
        ax.set_title('Action Magnitude Over Time')
        ax.grid(True, alpha=0.3)
        
        # 6. Risk distribution (bottom-right)
        ax = axes[1, 2]
        all_risks = risk_analysis['all_risks']
        
        ax.hist(all_risks, bins=min(15, len(all_risks)), alpha=0.7, color='lightcoral', edgecolor='black')
        ax.axvline(best_traj['total_risk'], color='darkred', linestyle='--', linewidth=2,
                  label=f"Selected: {best_traj['total_risk']:.3f}")
        ax.axvline(risk_analysis['mean_risk'], color='blue', linestyle=':', linewidth=2,
                  label=f"Mean: {risk_analysis['mean_risk']:.3f}")
        
        ax.set_xlabel('Total Risk')
        ax.set_ylabel('Frequency')
        ax.set_title('Risk Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_trajectory_comparison(self,
                                 results_list: List[Dict],
                                 labels: List[str],
                                 save_path: Optional[str] = None):
        """
        Compare multiple trajectory predictions.
        
        Args:
            results_list: List of trajectory prediction results
            labels: Labels for each result
            save_path: Path to save the plot
        """
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        colors = plt.cm.Set1(np.linspace(0, 1, len(results_list)))
        
        # 1. Trajectory paths
        ax = axes[0]
        for i, (results, label) in enumerate(zip(results_list, labels)):
            positions = results['best_trajectory']['positions']
            ax.plot(positions[:, 0], positions[:, 1], 
                   color=colors[i], linewidth=2, marker='o', markersize=4, label=label)
        
        ax.set_xlabel('X Position (m)')
        ax.set_ylabel('Y Position (m)')
        ax.set_title('Trajectory Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axis('equal')
        
        # 2. Risk comparison
        ax = axes[1]
        for i, (results, label) in enumerate(zip(results_list, labels)):
            step_risks = results['best_trajectory']['step_risks']
            ax.plot(range(len(step_risks)), step_risks, 
                   color=colors[i], linewidth=2, marker='s', markersize=4, label=label)
        
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Risk Score')
        ax.set_title('Risk Evolution Comparison')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 3. Total risk comparison
        ax = axes[2]
        total_risks = [results['best_trajectory']['total_risk'] for results in results_list]
        bars = ax.bar(labels, total_risks, color=colors, alpha=0.7)
        
        ax.set_ylabel('Total Risk')
        ax.set_title('Total Risk Comparison')
        ax.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, risk in zip(bars, total_risks):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{risk:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
