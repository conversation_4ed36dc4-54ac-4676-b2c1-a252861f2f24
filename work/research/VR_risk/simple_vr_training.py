#!/usr/bin/env python3
"""
Simple VR training script to test the fixes.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import time
import os

# Import our modules
from trajectory_prediction_model import LSTMTrajectoryPredictor, ActionSampler
from enhanced_risk_model import EnhancedRiskModel, TrajectoryChoiceModel
from vr_data_loader import create_vr_dataloaders, get_dataset_info

def simple_training_test():
    """Simple training test with VR data."""
    print("🚀 Simple VR Training Test")
    print("=" * 50)
    
    # Device configuration
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    try:
        # 1. Load VR dataset info
        print("\n📊 Loading VR dataset...")
        data_path = 'processed_data'
        dataset_info = get_dataset_info(data_path)
        
        print(f"Total samples: {dataset_info['total_samples']:,}")
        print(f"Subjects: {dataset_info['num_subjects']}")
        print(f"Crossings: {dataset_info['crossings']:,}")
        print(f"Non-crossings: {dataset_info['non_crossings']:,}")
        
        # 2. Create data loaders
        print("\n🔄 Creating data loaders...")
        train_loader, val_loader, test_loader, norm_stats = create_vr_dataloaders(
            data_path=data_path,
            batch_size=8,  # Very small batch for testing
            obs_len=10,
            pred_len=10,
            train_split=0.8,
            val_split=0.1,
            normalize=True,
            num_workers=0  # No multiprocessing
        )
        
        print(f"Train batches: {len(train_loader)}")
        print(f"Val batches: {len(val_loader)}")
        print(f"Test batches: {len(test_loader)}")
        
        # 3. Initialize models
        print("\n🤖 Initializing models...")
        trajectory_predictor = LSTMTrajectoryPredictor().to(device)
        risk_model = EnhancedRiskModel().to(device)
        action_sampler = ActionSampler()
        choice_model = TrajectoryChoiceModel(trajectory_predictor, risk_model)
        
        print(f"Total parameters: {sum(p.numel() for p in choice_model.parameters()):,}")
        print(f"Candidate actions: {action_sampler.num_actions}")
        
        # 4. Test forward pass
        print("\n🧪 Testing forward pass...")
        sample_batch = next(iter(train_loader))
        obs_seq, pred_seq, metadata = sample_batch
        obs_seq = obs_seq.to(device)
        pred_seq = pred_seq.to(device)
        
        print(f"Batch shapes:")
        print(f"  obs_seq: {obs_seq.shape}")
        print(f"  pred_seq: {pred_seq.shape}")
        
        # Test model forward pass
        current_state = obs_seq[:, -1]  # [B, state_dim]
        candidate_actions = action_sampler.get_candidates(device)
        
        with torch.no_grad():
            probabilities, scores = choice_model(obs_seq, current_state, candidate_actions)
        
        print(f"Forward pass successful!")
        print(f"  probabilities: {probabilities.shape}")
        print(f"  scores: {scores.shape}")
        
        # 5. Test training step
        print("\n🏋️ Testing training step...")
        optimizer = torch.optim.Adam(choice_model.parameters(), lr=1e-3)
        
        # Simple training step
        choice_model.train()
        optimizer.zero_grad()
        
        # Compute loss for first prediction step
        t = 0
        current_state = obs_seq[:, -1]  # Last observation
        target_state = pred_seq[:, t]   # Target state
        target_action = target_state[:, :2] - current_state[:, :2]  # Position change
        
        # Get probabilities
        probabilities, _ = choice_model(obs_seq, current_state, candidate_actions)
        
        # Find closest candidate action
        action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)
        action_distances = torch.norm(action_diffs, dim=2)
        target_action_idx = torch.argmin(action_distances, dim=1)
        
        # Compute loss
        batch_probs = probabilities[target_action_idx, torch.arange(obs_seq.shape[0])]
        loss = -torch.log(batch_probs + 1e-8).mean()

        # Test accuracy computation
        predicted_action_idx = torch.argmax(probabilities, dim=0)
        correct = (predicted_action_idx == target_action_idx).sum().item()
        accuracy = correct / obs_seq.shape[0]
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        print(f"Training step successful!")
        print(f"  Loss: {loss.item():.4f}")
        print(f"  Accuracy: {accuracy:.4f}")
        print(f"  Target action indices: {target_action_idx}")
        print(f"  Predicted action indices: {predicted_action_idx}")
        print(f"  Batch probabilities: {batch_probs}")
        
        # 6. Test multiple batches
        print("\n📚 Testing multiple batches...")
        total_loss = 0.0
        num_batches = min(5, len(train_loader))  # Test first 5 batches
        
        for i, (obs_seq, pred_seq, metadata) in enumerate(train_loader):
            if i >= num_batches:
                break
                
            obs_seq = obs_seq.to(device)
            pred_seq = pred_seq.to(device)
            
            optimizer.zero_grad()
            
            # Simple loss computation
            current_state = obs_seq[:, -1]
            target_state = pred_seq[:, 0]
            target_action = target_state[:, :2] - current_state[:, :2]
            
            probabilities, _ = choice_model(obs_seq, current_state, candidate_actions)
            
            action_diffs = candidate_actions.unsqueeze(0) - target_action.unsqueeze(1)
            action_distances = torch.norm(action_diffs, dim=2)
            target_action_idx = torch.argmin(action_distances, dim=1)
            
            batch_probs = probabilities[target_action_idx, torch.arange(obs_seq.shape[0])]
            loss = -torch.log(batch_probs + 1e-8).mean()

            # Compute accuracy
            predicted_action_idx = torch.argmax(probabilities, dim=0)
            correct = (predicted_action_idx == target_action_idx).sum().item()
            accuracy = correct / obs_seq.shape[0]
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            print(f"  Batch {i+1}/{num_batches}: Loss = {loss.item():.4f}, Acc = {accuracy:.4f}")
        
        avg_loss = total_loss / num_batches
        print(f"Average loss over {num_batches} batches: {avg_loss:.4f}")
        
        print("\n✅ All tests passed! VR training system is working.")
        return True
        
    except Exception as e:
        print(f"\n❌ Error in VR training test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the simple training test."""
    success = simple_training_test()
    
    if success:
        print("\n🎉 SUCCESS: VR training system is ready!")
        print("\nNext steps:")
        print("1. Run the full training notebook: vr_training_demo.ipynb")
        print("2. Or use this script as a starting point for custom training")
    else:
        print("\n⚠️  FAILED: Please check the errors above")

if __name__ == "__main__":
    main()
